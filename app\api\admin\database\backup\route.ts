import { NextRequest, NextResponse } from 'next/server'
import { DatabaseManager } from '../../../../../lib/services/database-manager'

// GET - List all backups
export async function GET(request: NextRequest) {
  try {
    console.log('🔧 Admin API: Fetching database backups')

    const backups = await DatabaseManager.listBackups()

    console.log('✅ Admin API: Database backups fetched successfully')

    return NextResponse.json(backups, {
      headers: {
        'Cache-Control': 'no-cache', // Always fresh for backups
        'Content-Type': 'application/json',
      },
    })
  } catch (error: any) {
    console.error('❌ Admin API: Error fetching backups:', error)
    return NextResponse.json(
      { error: 'Failed to fetch backups' },
      { status: 500 }
    )
  }
}

// POST - Create new backup
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, tables } = body

    if (!name) {
      return NextResponse.json(
        { error: 'Backup name is required' },
        { status: 400 }
      )
    }

    console.log(`🔧 Admin API: Creating backup: ${name}`)

    const backup = await DatabaseManager.createBackup(name, tables)

    console.log('✅ Admin API: Backup created successfully')

    return NextResponse.json(backup, { status: 201 })
  } catch (error: any) {
    console.error('❌ Admin API: Error creating backup:', error)
    return NextResponse.json(
      { error: 'Failed to create backup' },
      { status: 500 }
    )
  }
}

// DELETE - Delete backup
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const backupId = searchParams.get('id')

    if (!backupId) {
      return NextResponse.json(
        { error: 'Backup ID is required' },
        { status: 400 }
      )
    }

    console.log(`🔧 Admin API: Deleting backup: ${backupId}`)

    await DatabaseManager.deleteBackup(backupId)

    console.log('✅ Admin API: Backup deleted successfully')

    return NextResponse.json({ success: true })
  } catch (error: any) {
    console.error('❌ Admin API: Error deleting backup:', error)
    return NextResponse.json(
      { error: 'Failed to delete backup' },
      { status: 500 }
    )
  }
}
