"use client"

import { useState, useEffect, useMemo } from "react"
import { Check, X, Search, Eye, Trash2, ChevronLeft, ChevronRight, Copy, RefreshCw } from "lucide-react"
import { Order, User } from "../../../types"
import { formatDateRelative, formatDateForTable } from "../../../lib/utils/date-utils"
import { useAdminOrdersPaginated, useOrderCounts } from "../../../hooks/useAdminAuth"
import { toast } from "../../../hooks/use-toast"
import { useSearchParams } from "next/navigation"
import { useQueryClient } from '@tanstack/react-query'

type OrderStatus = 'all' | 'pending' | 'completed' | 'cancelled'
type TabType = 'all' | 'pending' | 'completed' | 'cancelled'

// ✅ PERFORMANCE: Debounce hook for search
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

export default function OrderManagement() {
  const searchParams = useSearchParams()
  const queryClient = useQueryClient()
  
  // Check for URL parameters to auto-filter orders
  const urlOrderId = searchParams.get('orderId')
  const urlStatus = searchParams.get('status') as TabType
  
  const [activeTab, setActiveTab] = useState<TabType>(urlStatus || 'all')
  const [searchTerm, setSearchTerm] = useState(urlOrderId || "")
  const [activeSearchTerm, setActiveSearchTerm] = useState(urlOrderId || "") // Only this triggers API calls
  const [selectedOrder, setSelectedOrder] = useState<any>(null)
  const [deleteLoading, setDeleteLoading] = useState<string | null>(null)
  const [updateLoading, setUpdateLoading] = useState<string | null>(null)
  const [optimisticUpdates, setOptimisticUpdates] = useState<Record<string, string>>({})
  const [currentPage, setCurrentPage] = useState(1)

  // Auto-trigger search if URL contains order ID
  useEffect(() => {
    if (urlOrderId) {
      console.log(`🔗 Direct link detected for order: ${urlOrderId}`)
      setSearchTerm(urlOrderId)
      setActiveSearchTerm(urlOrderId)
      setCurrentPage(1)
    }
    if (urlStatus && ['all', 'pending', 'completed', 'cancelled'].includes(urlStatus)) {
      setActiveTab(urlStatus)
    }
  }, [urlOrderId, urlStatus])

  // ✅ FIXED: Manual search trigger (no auto-search while typing)
  const {
    data: ordersData,
    isLoading: loading,
    error: fetchError,
    refetch
  } = useAdminOrdersPaginated(currentPage, 20, activeTab, activeSearchTerm)

  // ✅ NEW: Get order counts for tab labels
  const { data: orderCounts } = useOrderCounts()

  const ordersWithDetails = ordersData?.orders || []
  const totalPages = ordersData?.totalPages || 0
  const totalCount = ordersData?.totalCount || 0
  const error = fetchError ? 'حدث خطأ أثناء جلب البيانات' : null



  // Reset to page 1 when search or filter changes
  useEffect(() => {
    setCurrentPage(1)
  }, [activeSearchTerm, activeTab])

  // ✅ SEARCH FUNCTIONS: Manual search trigger
  const handleSearch = () => {
    const trimmedSearch = searchTerm.trim()
    console.log(`🔍 Performing search for: "${trimmedSearch}"`)
    setActiveSearchTerm(trimmedSearch)
    setCurrentPage(1)
  }

  const handleSearchKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  const clearSearch = () => {
    setSearchTerm("")
    setActiveSearchTerm("")
    setCurrentPage(1)
  }

  // ✅ NEW: Copy to clipboard functionality
  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast({
        title: "تم النسخ",
        description: `تم نسخ ${label} بنجاح`,
        variant: "default",
      })
    } catch (err) {
      console.error('Failed to copy:', err)
      // Fallback for older browsers
      const textArea = document.createElement('textarea')
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.select()
      try {
        document.execCommand('copy')
        toast({
          title: "تم النسخ",
          description: `تم نسخ ${label} بنجاح`,
          variant: "default",
        })
      } catch (fallbackErr) {
        toast({
          title: "خطأ",
          description: "فشل في نسخ النص",
          variant: "destructive",
        })
      }
      document.body.removeChild(textArea)
    }
  }

  const updateOrderStatus = async (orderId: string, status: Order['status']) => {
    try {
      setUpdateLoading(orderId)
      // Optimistic update - immediately show the new status
      setOptimisticUpdates(prev => ({ ...prev, [orderId]: status }))
      console.log(`🔧 OrderManagement: Updating order ${orderId} status to ${status}`)

      const response = await fetch('/api/admin/orders', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ orderId, status })
      })

      if (response.status === 401) {
        toast({
          title: "خطأ",
          description: 'غير مسموح بالوصول - يرجى تسجيل الدخول',
          variant: "destructive",
        })
        return
      }
      
      if (response.status === 403) {
        toast({
          title: "خطأ", 
          description: 'ليس لديك صلاحيات إدارية',
          variant: "destructive",
        })
        return
      }

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
        throw new Error(errorData.error || 'Failed to update order status')
      }

      console.log('✅ OrderManagement: Order status updated successfully')
      
      // Since we have no cache, just trigger a simple refetch
      console.log('🔄 OrderManagement: Triggering fresh data fetch (no cache)...')
      
      // Small delay to ensure server has processed the change
      await new Promise(resolve => setTimeout(resolve, 300))
      
      // Trigger refetch - since cache is disabled, this will always fetch fresh data
      await refetch()
      
      console.log('✅ OrderManagement: Fresh data fetched successfully')
      
      toast({
        title: "تم التحديث",
        description: `تم تحديث حالة الطلب إلى ${getStatusText(status)}`,
        variant: "default",
      })
    } catch (error) {
      console.error('Error updating order status:', error)
      toast({
        title: "خطأ",
        description: 'حدث خطأ أثناء تحديث حالة الطلب',
        variant: "destructive",
      })
    } finally {
      setUpdateLoading(null)
      // Clear optimistic update after processing
      setOptimisticUpdates(prev => {
        const newState = { ...prev }
        delete newState[orderId]
        return newState
      })
    }
  }

  const deleteOrder = async (orderId: string) => {
    if (!confirm('هل أنت متأكد من حذف هذا الطلب؟ سيتم استرداد المبلغ للعميل إذا كان الطلب مكتملاً.')) {
      return
    }

    try {
      setDeleteLoading(orderId)
      console.log(`🔧 OrderManagement: Deleting order ${orderId}`)

      const response = await fetch('/api/admin/orders', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ orderId })
      })

      if (!response.ok) {
        throw new Error('Failed to delete order')
      }

      console.log('✅ OrderManagement: Order deleted successfully')
      // Refresh the current page
      await refetch()
    } catch (error) {
      console.error('Error deleting order:', error)
      toast({
        title: "خطأ",
        description: 'حدث خطأ أثناء حذف الطلب',
        variant: "destructive",
      })
    } finally {
      setDeleteLoading(null)
    }
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab)
    setCurrentPage(1)
  }

  // ✅ FIXED: Remove client-side filtering - use server-side results only
  // No more double filtering! Server already handles search and pagination
  const filteredOrders = ordersWithDetails

  const getTabLabel = (tab: TabType) => {
    // ✅ FIXED: Use correct counts from server for each tab
    const counts = orderCounts || { all: 0, pending: 0, completed: 0, cancelled: 0 }

    switch (tab) {
      case 'all':
        return `جميع الطلبات (${counts.all})`
      case 'pending':
        return `قيد المعالجة (${counts.pending})`
      case 'completed':
        return `مكتملة (${counts.completed})`
      case 'cancelled':
        return `ملغية (${counts.cancelled})`
      default:
        return tab
    }
  }

  // Use the centralized date formatting utility
  const formatDate = (dateString: string) => {
    return formatDateRelative(dateString)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-500/20 text-green-400 border-green-500/30"
      case "pending":
        return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30"
      case "cancelled":
        return "bg-red-500/20 text-red-400 border-red-500/30"
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/30"
    }
  }

  const getCardBorderColor = (status: string) => {
    switch (status) {
      case "completed":
        return "border-l-4 border-l-green-500 bg-green-500/5"
      case "pending":
        return "border-l-4 border-l-yellow-500 bg-yellow-500/5"
      case "cancelled":
        return "border-l-4 border-l-red-500 bg-red-500/5"
      default:
        return "border-l-4 border-l-gray-500 bg-gray-500/5"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return "✅"
      case "pending":
        return "⏳"
      case "cancelled":
        return "❌"
      default:
        return "❓"
    }
  }

  // Get effective status (optimistic update or actual status)
  const getEffectiveStatus = (orderId: string, actualStatus: string) => {
    return optimisticUpdates[orderId] || actualStatus
  }

  const getActionButtonStyle = (action: string, currentStatus: string) => {
    switch (action) {
      case "complete":
        return currentStatus === "completed" 
          ? "bg-green-600/20 border-green-500/30 text-green-400 hover:bg-green-600/30" 
          : "bg-green-600/10 border-green-500/20 text-green-500 hover:bg-green-600/20 hover:border-green-500/40"
      case "cancel":
        return "bg-red-600/10 border-red-500/20 text-red-500 hover:bg-red-600/20 hover:border-red-500/40"
      case "view":
        return "bg-blue-600/10 border-blue-500/20 text-blue-400 hover:bg-blue-600/20 hover:border-blue-500/40"
      case "delete":
        return "bg-red-700/10 border-red-600/20 text-red-600 hover:bg-red-700/20 hover:border-red-600/40"
      default:
        return "bg-gray-600/10 border-gray-500/20 text-gray-400 hover:bg-gray-600/20"
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "completed":
        return "مكتمل"
      case "pending":
        return "قيد المعالجة"
      case "cancelled":
        return "ملغي"
      default:
        return "غير محدد"
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="bg-gray-800 border border-gray-600 rounded-lg p-6">
          <div className="text-center py-8">
            <div className="text-white">جاري تحميل الطلبات...</div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-gray-800 border border-gray-600 rounded-lg p-6">
          <div className="text-center py-8">
            <div className="text-red-400 mb-4">{error}</div>
            <button
              onClick={() => window.location.reload()}
              className="bg-red-800 hover:bg-red-900 text-white px-4 py-2 rounded-lg"
            >
              إعادة المحاولة
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full">
      <div className="bg-gray-800 border border-gray-600 rounded-lg p-3 md:p-4 h-full flex flex-col">
        <div className="flex flex-col gap-4 mb-6">
          <h2 className="text-xl font-bold text-white">إدارة الطلبات</h2>

          {/* Enhanced Search with Manual Trigger - Fixed for mobile */}
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2">
            <div className="relative flex-1">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
                placeholder="البحث: رقم الطلب، اسم العميل، معرف اللعبة، المبلغ..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
                onKeyPress={handleSearchKeyPress}
                className="w-full pl-4 pr-10 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-red-800"
            />
            </div>
            <div className="flex items-center gap-2 shrink-0">
              <button
                onClick={handleSearch}
                disabled={loading}
                className="px-4 py-2 bg-red-800 hover:bg-red-700 border border-red-600 rounded-lg text-white text-sm transition-colors disabled:opacity-50"
                title="بحث"
              >
                بحث
              </button>
              {activeSearchTerm && (
                <button
                  onClick={clearSearch}
                  disabled={loading}
                  className="px-3 py-2 bg-gray-700 hover:bg-gray-600 border border-gray-600 rounded-lg text-gray-400 hover:text-white text-sm transition-colors disabled:opacity-50"
                  title="مسح البحث"
                >
                  مسح
                </button>
              )}
              <button
                onClick={() => refetch()}
                disabled={loading}
                className="p-2 bg-gray-700 hover:bg-gray-600 border border-gray-600 rounded-lg text-gray-400 hover:text-white transition-colors disabled:opacity-50"
                title="تحديث البيانات"
              >
                <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              </button>
            </div>
          </div>
        </div>

        {/* Enhanced Status Tabs */}
        <div className="flex flex-wrap gap-2 mb-4 border-b border-gray-600 overflow-x-auto flex-shrink-0">
          {(['all', 'pending', 'completed', 'cancelled'] as TabType[]).map((tab) => (
            <button
              key={tab}
              onClick={() => handleTabChange(tab)}
              className={`px-4 py-2 text-sm font-medium rounded-t-lg transition-all duration-200 whitespace-nowrap flex items-center gap-2 ${
                activeTab === tab
                  ? 'bg-red-800 text-white border-b-2 border-red-400 shadow-lg'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700 hover:shadow-md'
              }`}
            >
              {tab !== 'all' && <span className="text-xs">{getStatusIcon(tab)}</span>}
              {getTabLabel(tab)}
            </button>
          ))}
        </div>

        {/* Mobile View - Enhanced with color-coded cards */}
        <div className="block md:hidden">
          <div className="space-y-4">
            {filteredOrders.map((order: any) => {
              const effectiveStatus = getEffectiveStatus(order.id, order.status)
              return (
            <div key={order.id} className={`relative ${getCardBorderColor(effectiveStatus)} border border-gray-600 rounded-lg p-4 hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02] ${updateLoading === order.id ? 'pointer-events-none' : ''}`}>
              {updateLoading === order.id && (
                <div className="absolute inset-0 bg-black/20 rounded-lg flex items-center justify-center z-10">
                  <div className="bg-gray-800/90 px-3 py-2 rounded-lg flex items-center gap-2 text-white text-sm">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    جاري التحديث...
                  </div>
                </div>
              )}
              {/* Header with Order ID and Status */}
                <div className="flex items-start justify-between mb-3">
                  <div className="flex flex-col min-w-0 flex-1">
                  <div className="flex items-center gap-2">
                    <span className="text-red-400 font-bold text-sm">#{order.id.slice(-8)}</span>
                    <button
                      onClick={() => copyToClipboard(order.id, 'رقم الطلب')}
                      className="p-1 hover:bg-gray-600 rounded text-gray-400 hover:text-white transition-colors"
                      title="نسخ رقم الطلب"
                    >
                      <Copy className="w-3 h-3" />
                    </button>
                  </div>
                  <span className="text-gray-500 text-xs">{formatDate(order.created_at)}</span>
                </div>
                  <div className={`px-3 py-1.5 rounded-full text-xs font-medium whitespace-nowrap border ${getStatusColor(effectiveStatus)} flex items-center gap-1`}>
                    <span className="text-sm">{getStatusIcon(effectiveStatus)}</span>
                  {getStatusText(effectiveStatus)}
                </div>
              </div>

              {/* Product and Customer Info */}
              <div className="space-y-2 mb-3">
                  <div className="text-white text-sm font-medium break-words">{order.product_name}</div>
                  <div className="space-y-1">
                    <div className="text-xs">
                      <span className="text-gray-400">العميل: </span>
                      <span className="text-gray-300">{order.user_name}</span>
                    </div>
                    <div className="flex items-center gap-1 text-xs">
                      <span className="text-gray-400">Game ID: </span>
                      <span className="text-gray-300 break-all">{order.game_id}</span>
                    <button
                      onClick={() => copyToClipboard(order.game_id, 'معرف اللعبة')}
                      className="p-0.5 hover:bg-gray-600 rounded text-gray-400 hover:text-white transition-colors"
                      title="نسخ معرف اللعبة"
                    >
                      <Copy className="w-3 h-3" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Price and Actions */}
              <div className="flex items-center justify-between pt-3 border-t border-gray-700">
                <div className="flex flex-col">
                  <span className="text-white font-bold text-lg">${order.price}</span>
                  <span className="text-gray-400 text-xs">{order.product_name}</span>
                </div>
                  <div className="flex items-center gap-2">
                  <button
                    onClick={() => setSelectedOrder(order)}
                    className={`p-2 rounded-lg border transition-all duration-200 ${getActionButtonStyle('view', order.status)}`}
                    title="عرض التفاصيل"
                  >
                    <Eye className="w-4 h-4" />
                  </button>
                  {/* Show Approve button only for pending orders */}
                  {effectiveStatus === "pending" && (
                    <button
                      onClick={() => updateOrderStatus(order.id, "completed")}
                      disabled={updateLoading === order.id}
                      className={`p-2 rounded-lg border transition-all duration-200 disabled:opacity-50 ${getActionButtonStyle('complete', order.status)}`}
                      title="الموافقة على الطلب"
                    >
                      {updateLoading === order.id ? (
                        <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                      ) : (
                        <Check className="w-4 h-4" />
                      )}
                    </button>
                  )}
                  {/* Show Decline button for all orders */}
                  <button
                    onClick={() => updateOrderStatus(order.id, "cancelled")}
                    disabled={updateLoading === order.id}
                    className={`p-2 rounded-lg border transition-all duration-200 disabled:opacity-50 ${getActionButtonStyle('cancel', order.status)}`}
                    title="رفض الطلب"
                  >
                    {updateLoading === order.id ? (
                      <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                    ) : (
                      <X className="w-4 h-4" />
                    )}
                  </button>
                  {/* Show Remove button for all orders */}
                  <button
                    onClick={() => deleteOrder(order.id)}
                    disabled={deleteLoading === order.id}
                    className={`p-2 rounded-lg border transition-all duration-200 disabled:opacity-50 ${getActionButtonStyle('delete', order.status)}`}
                    title="حذف الطلب"
                  >
                    {deleteLoading === order.id ? (
                      <div className="w-4 h-4 border-2 border-red-500 border-t-transparent rounded-full animate-spin"></div>
                    ) : (
                      <Trash2 className="w-4 h-4" />
                    )}
                  </button>
                </div>
              </div>
            </div>
          )})}
          </div>
        </div>

        {/* Desktop View - Fully Responsive */}
        <div className="hidden md:block flex-1">
          <div className="overflow-auto bg-gray-800/50 rounded-lg h-full max-h-[calc(100vh-20rem)]">
            <table className="w-full table-auto min-w-0">
            <thead>
              <tr className="border-b border-gray-600">
                <th className="text-right py-4 px-3 text-gray-300 font-medium text-sm min-w-[110px]">رقم الطلب</th>
                <th className="text-right py-4 px-3 text-gray-300 font-medium text-sm min-w-[90px]">العميل</th>
                <th className="text-right py-4 px-3 text-gray-300 font-medium text-sm min-w-[130px]">المنتج</th>
                <th className="text-right py-4 px-3 text-gray-300 font-medium text-sm min-w-[100px]">Game ID</th>
                <th className="text-right py-4 px-3 text-gray-300 font-medium text-sm min-w-[70px]">المبلغ</th>
                <th className="text-right py-4 px-3 text-gray-300 font-medium text-sm min-w-[90px]">التاريخ</th>
                <th className="text-right py-4 px-3 text-gray-300 font-medium text-sm min-w-[80px]">الحالة</th>
                <th className="text-right py-4 px-3 text-gray-300 font-medium text-sm min-w-[130px]">الإجراءات</th>
              </tr>
            </thead>
            <tbody>
                  {filteredOrders.map((order: any) => (
                <tr key={order.id} className={`relative border-b border-gray-600/50 hover:shadow-lg transition-all duration-300 ${getCardBorderColor(order.status).replace('border-l-4', 'border-l-2')} ${updateLoading === order.id ? 'pointer-events-none opacity-70' : ''}`}>
                  <td className="py-4 px-3">
                    <div className="flex items-center gap-1">
                      <span className="text-red-400 font-medium text-sm truncate max-w-[90px]" title={order.id}>
                        #{order.id.slice(-6)}
                      </span>
                      <button
                        onClick={() => copyToClipboard(order.id, 'رقم الطلب')}
                        className="p-0.5 hover:bg-gray-600 rounded text-gray-400 hover:text-white transition-colors flex-shrink-0"
                        title="نسخ رقم الطلب"
                      >
                        <Copy className="w-3 h-3" />
                      </button>
                    </div>
                  </td>
                  <td className="py-4 px-3 text-white text-sm truncate max-w-[90px]" title={order.user_name}>
                    {order.user_name}
                  </td>
                  <td className="py-4 px-3 text-gray-300 text-sm truncate max-w-[130px]" title={order.product_name}>
                    {order.product_name}
                  </td>
                  <td className="py-4 px-3">
                    <div className="flex items-center gap-1">
                      <span className="text-gray-400 text-sm truncate max-w-[80px]" title={order.game_id}>
                        {order.game_id}
                      </span>
                      <button
                        onClick={() => copyToClipboard(order.game_id, 'معرف اللعبة')}
                        className="p-0.5 hover:bg-gray-600 rounded text-gray-400 hover:text-white transition-colors flex-shrink-0"
                        title="نسخ معرف اللعبة"
                      >
                        <Copy className="w-3 h-3" />
                      </button>
                    </div>
                  </td>
                  <td className="py-4 px-3 text-white font-semibold text-sm">
                    ${order.price}
                  </td>
                  <td className="py-4 px-3 text-gray-400 text-xs">
                    <div className="flex flex-col">
                      <span className="text-xs">{formatDateForTable(order.created_at).date}</span>
                      <span className="text-xs text-gray-500">{formatDateForTable(order.created_at).time}</span>
                    </div>
                  </td>
                  <td className="py-4 px-3">
                    <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium whitespace-nowrap border ${getStatusColor(order.status)}`}>
                      <span className="text-sm">{getStatusIcon(order.status)}</span>
                      <span className="hidden lg:inline text-xs">{getStatusText(order.status)}</span>
                    </div>
                  </td>
                  <td className="py-4 px-3">
                    <div className="flex items-center gap-1">
                      <button
                        onClick={() => setSelectedOrder(order)}
                        className={`p-1.5 rounded border transition-all duration-200 ${getActionButtonStyle('view', order.status)}`}
                        title="عرض التفاصيل"
                      >
                        <Eye className="w-3.5 h-3.5" />
                      </button>
                      {/* Show Approve button only for pending orders */}
                      {order.status === "pending" && (
                        <button
                          onClick={() => updateOrderStatus(order.id, "completed")}
                          disabled={updateLoading === order.id}
                          className={`p-1.5 rounded border transition-all duration-200 disabled:opacity-50 ${getActionButtonStyle('complete', order.status)}`}
                          title="الموافقة على الطلب"
                        >
                          {updateLoading === order.id ? (
                            <div className="w-3.5 h-3.5 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                          ) : (
                            <Check className="w-3.5 h-3.5" />
                          )}
                        </button>
                      )}
                      {/* Show Decline button for all orders */}
                      <button
                        onClick={() => updateOrderStatus(order.id, "cancelled")}
                        disabled={updateLoading === order.id}
                        className={`p-1.5 rounded border transition-all duration-200 disabled:opacity-50 ${getActionButtonStyle('cancel', order.status)}`}
                        title="رفض الطلب"
                      >
                        {updateLoading === order.id ? (
                          <div className="w-3.5 h-3.5 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                        ) : (
                          <X className="w-3.5 h-3.5" />
                        )}
                      </button>
                      {/* Show Remove button for all orders */}
                      <button
                        onClick={() => deleteOrder(order.id)}
                        disabled={deleteLoading === order.id}
                        className={`p-1.5 rounded border transition-all duration-200 disabled:opacity-50 ${getActionButtonStyle('delete', order.status)}`}
                        title="حذف الطلب"
                      >
                        {deleteLoading === order.id ? (
                          <div className="w-3.5 h-3.5 border-2 border-red-500 border-t-transparent rounded-full animate-spin"></div>
                        ) : (
                          <Trash2 className="w-3.5 h-3.5" />
                        )}
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
            </div>
        </div>

        {!loading && filteredOrders.length === 0 && (
          <div className="text-center py-8">
            <p className="text-gray-400">
              {activeSearchTerm ? 'لا توجد طلبات تطابق البحث' : 'لا توجد طلبات'}
            </p>
          </div>
        )}

        {/* Pagination */}
        {totalCount > 20 && (
          <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-600">
            <div className="text-sm text-gray-400">
              صفحة {currentPage} - عرض 20 طلبات لكل صفحة
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="p-2 rounded-lg bg-gray-700 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <ChevronRight className="w-4 h-4 text-white" />
              </button>

              <span className="px-3 py-1 text-sm text-white bg-red-800 rounded">
                {currentPage}
              </span>

              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="p-2 rounded-lg bg-gray-700 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <ChevronLeft className="w-4 h-4 text-white" />
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Enhanced Order Details Modal */}
      {selectedOrder && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className={`${getCardBorderColor(selectedOrder.status)} border border-gray-600 rounded-lg p-6 max-w-md w-full shadow-2xl transform transition-all duration-300`}>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-white">تفاصيل الطلب</h3>
              <div className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(selectedOrder.status)} flex items-center gap-1`}>
                <span className="text-sm">{getStatusIcon(selectedOrder.status)}</span>
                {getStatusText(selectedOrder.status)}
              </div>
            </div>
            <div className="space-y-4">
              <div className="bg-gray-700/30 p-3 rounded-lg">
                <span className="text-gray-400 text-sm">رقم الطلب</span>
                <div className="flex items-center gap-2 mt-1">
                  <span className="text-white font-mono text-sm">{selectedOrder.id}</span>
                  <button
                    onClick={() => copyToClipboard(selectedOrder.id, 'رقم الطلب')}
                    className="p-1 hover:bg-gray-600 rounded text-gray-400 hover:text-white transition-colors"
                  >
                    <Copy className="w-3 h-3" />
                  </button>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-gray-700/30 p-3 rounded-lg">
                  <span className="text-gray-400 text-sm">العميل</span>
                  <div className="text-white font-medium mt-1">{selectedOrder.user_name}</div>
                </div>
                <div className="bg-gray-700/30 p-3 rounded-lg">
                  <span className="text-gray-400 text-sm">المبلغ</span>
                  <div className="text-white font-bold text-lg mt-1">${selectedOrder.price}</div>
                </div>
              </div>
              <div className="bg-gray-700/30 p-3 rounded-lg">
                <span className="text-gray-400 text-sm">المنتج</span>
                <div className="text-white font-medium mt-1">{selectedOrder.product_name}</div>
              </div>
              <div className="bg-gray-700/30 p-3 rounded-lg">
                <span className="text-gray-400 text-sm">Game ID</span>
                <div className="flex items-center gap-2 mt-1">
                  <span className="text-white font-mono text-sm">{selectedOrder.game_id}</span>
                  <button
                    onClick={() => copyToClipboard(selectedOrder.game_id, 'معرف اللعبة')}
                    className="p-1 hover:bg-gray-600 rounded text-gray-400 hover:text-white transition-colors"
                  >
                    <Copy className="w-3 h-3" />
                  </button>
                </div>
              </div>
              <div className="bg-gray-700/30 p-3 rounded-lg">
                <span className="text-gray-400 text-sm">تاريخ الطلب</span>
                <div className="text-white mt-1">{formatDate(selectedOrder.created_at)}</div>
              </div>
            </div>
            <div className="flex gap-3 mt-6">
              <button
                onClick={() => setSelectedOrder(null)}
                className="flex-1 bg-gray-700 hover:bg-gray-600 text-white py-3 px-4 rounded-lg transition-colors font-medium"
              >
                إغلاق
              </button>
              {/* Show Approve button only for pending orders */}
              {selectedOrder.status === "pending" && (
                <button
                  onClick={() => {
                    updateOrderStatus(selectedOrder.id, "completed")
                    setSelectedOrder(null)
                  }}
                  disabled={updateLoading === selectedOrder.id}
                  className={`flex-1 py-3 px-4 rounded-lg transition-colors font-medium disabled:opacity-50 ${getActionButtonStyle('complete', selectedOrder.status)}`}
                >
                  {updateLoading === selectedOrder.id ? (
                    <div className="flex items-center justify-center gap-2">
                      <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                      جاري التحديث...
                    </div>
                  ) : (
                    "الموافقة على الطلب"
                  )}
                </button>
              )}
              {/* Show Decline button for all orders */}
              <button
                onClick={() => {
                  updateOrderStatus(selectedOrder.id, "cancelled")
                  setSelectedOrder(null)
                }}
                disabled={updateLoading === selectedOrder.id}
                className={`flex-1 py-3 px-4 rounded-lg transition-colors font-medium disabled:opacity-50 ${getActionButtonStyle('cancel', selectedOrder.status)}`}
              >
                {updateLoading === selectedOrder.id ? (
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                    جاري التحديث...
                  </div>
                ) : (
                  "رفض الطلب"
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
