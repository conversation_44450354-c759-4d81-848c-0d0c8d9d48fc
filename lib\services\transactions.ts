/**
 * Transactions Service
 * Handles transaction-related operations using Supabase
 */

import { supabaseAdmin } from '../supabase-admin'
import { Transaction } from '../../types'

export class TransactionService {
  private static supabase = supabaseAdmin

  /**
   * Get user transactions with order information
   */
  static async getUserTransactions(userId: string, limit: number = 10): Promise<Transaction[]> {
    try {
      console.log(`💰 Fetching transactions for user: ${userId}`)

      const { data, error } = await this.supabase
        .from('transactions')
        .select(`
          *,
          orders!transactions_order_id_fkey (
            id,
            status,
            product_name
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit)

      if (error) {
        console.error('❌ Get user transactions error:', error)
        throw new Error('Failed to fetch transactions')
      }

      console.log(`✅ Fetched ${data.length} transactions for user`)
      return data.map(this.mapSupabaseTransactionToAppTransaction)
    } catch (error: any) {
      console.error('❌ Get user transactions failed:', error)
      throw new Error('حدث خطأ أثناء جلب المعاملات')
    }
  }

  /**
   * Create new transaction with full audit trail
   */
  static async createTransaction(transactionData: {
    userId: string
    type: 'deposit' | 'purchase' | 'refund' | 'admin_adjustment' | 'admin_deposit' | 'admin_deduction'
    amount: number
    description?: string
    orderId?: string
    adminId?: string
    adminNotes?: string
    metadata?: any
  }): Promise<Transaction> {
    try {
      console.log('💳 Creating new transaction:', transactionData)

      // Get current user balance
      const { data: userData, error: userError } = await this.supabase
        .from('user_profiles')
        .select('balance')
        .eq('id', transactionData.userId)
        .single()

      if (userError) {
        throw new Error('Failed to get user balance')
      }

      const previousBalance = userData.balance || 0
      let newBalance = previousBalance

      // Calculate new balance based on transaction type
      switch (transactionData.type) {
        case 'deposit':
        case 'admin_deposit':
        case 'refund':
          newBalance = previousBalance + Math.abs(transactionData.amount)
          break
        case 'purchase':
        case 'admin_deduction':
          newBalance = previousBalance - Math.abs(transactionData.amount)
          break
        case 'admin_adjustment':
          newBalance = previousBalance + transactionData.amount // Can be positive or negative
          break
      }

      // Ensure balance doesn't go negative
      if (newBalance < 0) {
        throw new Error('Insufficient balance for this transaction')
      }

      // Create transaction record
      const { data, error } = await this.supabase
        .from('transactions')
        .insert({
          user_id: transactionData.userId,
          admin_id: transactionData.adminId || null,
          order_id: transactionData.orderId || null,
          type: transactionData.type,
          amount: Math.abs(transactionData.amount),
          previous_balance: previousBalance,
          new_balance: newBalance,
          description: transactionData.description || null,
          admin_notes: transactionData.adminNotes || null,
          metadata: transactionData.metadata || {}
        })
        .select()
        .single()

      if (error) {
        console.error('❌ Create transaction error:', error)
        throw new Error('Failed to create transaction')
      }

      // Update user balance
      const { error: balanceError } = await this.supabase
        .from('user_profiles')
        .update({ balance: newBalance })
        .eq('id', transactionData.userId)

      if (balanceError) {
        console.error('❌ Update balance error:', balanceError)
        throw new Error('Failed to update user balance')
      }

      console.log('✅ Transaction created successfully')
      return this.mapSupabaseTransactionToAppTransaction(data)
    } catch (error: any) {
      console.error('❌ Create transaction failed:', error)
      throw new Error(error.message || 'حدث خطأ أثناء إنشاء المعاملة')
    }
  }

  /**
   * Admin adds balance to user
   */
  static async adminAddBalance(
    userId: string,
    amount: number,
    adminId: string,
    notes?: string
  ): Promise<Transaction> {
    return this.createTransaction({
      userId,
      type: 'admin_deposit',
      amount: Math.abs(amount),
      description: `إضافة رصيد من الإدارة: $${amount}`,
      adminId,
      adminNotes: notes || 'إضافة رصيد من الإدارة',
      metadata: {
        action: 'admin_add_balance',
        original_amount: amount
      }
    })
  }

  /**
   * Admin deducts balance from user
   */
  static async adminDeductBalance(
    userId: string,
    amount: number,
    adminId: string,
    notes?: string
  ): Promise<Transaction> {
    return this.createTransaction({
      userId,
      type: 'admin_deduction',
      amount: Math.abs(amount),
      description: `خصم رصيد من الإدارة: $${amount}`,
      adminId,
      adminNotes: notes || 'خصم رصيد من الإدارة',
      metadata: {
        action: 'admin_deduct_balance',
        original_amount: amount
      }
    })
  }

  /**
   * Admin sets exact balance (creates adjustment transaction)
   */
  static async adminSetBalance(
    userId: string,
    newBalance: number,
    adminId?: string,
    notes?: string
  ): Promise<Transaction> {
    // Get current balance
    const { data: userData, error } = await this.supabase
      .from('user_profiles')
      .select('balance')
      .eq('id', userId)
      .single()

    if (error) {
      throw new Error('Failed to get current balance')
    }

    const currentBalance = userData.balance || 0
    const difference = newBalance - currentBalance

    if (difference === 0) {
      throw new Error('New balance is the same as current balance')
    }

    return this.createTransaction({
      userId,
      type: 'admin_adjustment',
      amount: difference, // Can be positive or negative
      description: `تعديل الرصيد من الإدارة: من $${currentBalance} إلى $${newBalance}`,
      adminId,
      adminNotes: notes || 'تعديل الرصيد من الإدارة',
      metadata: {
        action: 'admin_set_balance',
        previous_balance: currentBalance,
        new_balance: newBalance,
        difference: difference
      }
    })
  }

  /**
   * Get transaction by ID
   */
  static async getTransactionById(transactionId: string): Promise<Transaction | null> {
    try {
      const { data, error } = await this.supabase
        .from('transactions')
        .select('*')
        .eq('id', transactionId)
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          return null // Transaction not found
        }
        throw error
      }

      return this.mapSupabaseTransactionToAppTransaction(data)
    } catch (error: any) {
      console.error('❌ Get transaction by ID failed:', error)
      throw new Error('حدث خطأ أثناء جلب المعاملة')
    }
  }

  /**
   * Get all transactions for admin with server-side pagination
   * ✅ PERFORMANCE: Only fetches the requested page, includes total count
   */
  static async getAllTransactionsPaginated(
    page: number = 1, 
    pageSize: number = 20
  ): Promise<{
    transactions: Transaction[]
    totalCount: number
    totalPages: number
  }> {
    try {
      console.log(`💰 Fetching paginated admin transactions - page: ${page}, pageSize: ${pageSize}`)

      // Calculate offset for pagination
      const offset = (page - 1) * pageSize

      // First, get the total count of all transactions
      const { count, error: countError } = await this.supabase
        .from('transactions')
        .select('*', { count: 'exact', head: true })

      if (countError) {
        console.error('❌ Get admin transaction count error:', countError)
        throw new Error('Failed to fetch transaction count')
      }

      const totalCount = count || 0
      const totalPages = Math.ceil(totalCount / pageSize)

      // Then get the actual transactions for this page with user info
      const { data, error } = await this.supabase
        .from('transactions')
        .select(`
          *,
          orders!transactions_order_id_fkey (
            id,
            status,
            product_name
          ),
          user_profiles!transactions_user_id_fkey (
            name,
            email
          )
        `)
        .order('created_at', { ascending: false })
        .range(offset, offset + pageSize - 1)

      if (error) {
        console.error('❌ Get paginated admin transactions error:', error)
        throw new Error('Failed to fetch transactions')
      }

      console.log(`✅ Fetched ${data.length} admin transactions (page ${page}/${totalPages})`)
      
      return {
        transactions: data.map(this.mapSupabaseTransactionToAppTransaction),
        totalCount,
        totalPages
      }
    } catch (error: any) {
      console.error('❌ Get paginated admin transactions failed:', error)
      throw new Error('حدث خطأ أثناء جلب المعاملات')
    }
  }

  /**
   * Get user balance from transactions
   */
  static async getUserBalance(userId: string): Promise<number> {
    try {
      const { data, error } = await this.supabase
        .from('transactions')
        .select('amount, type')
        .eq('user_id', userId)

      if (error) {
        console.error('❌ Get user balance error:', error)
        throw new Error('Failed to calculate balance')
      }

      let balance = 0
      data.forEach(transaction => {
        if (transaction.type === 'deposit' || transaction.type === 'refund') {
          balance += transaction.amount
        } else if (transaction.type === 'purchase') {
          balance -= transaction.amount
        } else if (transaction.type === 'admin_adjustment') {
          balance += transaction.amount // Can be negative for deductions
        }
      })

      return balance
    } catch (error: any) {
      console.error('❌ Get user balance failed:', error)
      throw new Error('حدث خطأ أثناء حساب الرصيد')
    }
  }

  /**
   * Create transaction for order purchase
   */
  static async createOrderPurchaseTransaction(
    userId: string,
    orderId: string,
    amount: number,
    productName: string
  ): Promise<Transaction> {
    return this.createTransaction({
      userId,
      type: 'purchase',
      amount: Math.abs(amount),
      description: `شراء ${productName}`,
      orderId,
      metadata: {
        action: 'order_purchase',
        product_name: productName
      }
    })
  }

  /**
   * Create transaction for order refund
   */
  static async createOrderRefundTransaction(
    userId: string,
    orderId: string,
    amount: number,
    productName: string,
    adminId?: string,
    reason?: string
  ): Promise<Transaction> {
    return this.createTransaction({
      userId,
      type: 'refund',
      amount: Math.abs(amount),
      description: `استرداد ${productName}`,
      orderId,
      adminId,
      adminNotes: reason || 'استرداد بسبب إلغاء الطلب',
      metadata: {
        action: 'order_refund',
        product_name: productName,
        refund_reason: reason
      }
    })
  }

  /**
   * Map Supabase transaction to app transaction format
   */
  private static mapSupabaseTransactionToAppTransaction(transaction: any): Transaction {
    return {
      id: transaction.id,
      userId: transaction.user_id,
      type: transaction.type === 'deposit' || transaction.type === 'admin_deposit' || transaction.type === 'refund' ? 'credit' : 'debit',
      amount: transaction.amount,
      description: transaction.description || '',
      orderId: transaction.order_id,
      createdAt: transaction.created_at,
      adminId: transaction.admin_id,
      // Include order status for better user experience
      orderStatus: transaction.orders?.status || null,
      orderProductName: transaction.orders?.product_name || null
    }
  }

  /**
   * Get user transactions with server-side pagination
   * ✅ PERFORMANCE: Only fetches the requested page, includes total count
   */
  static async getUserTransactionsPaginated(
    userId: string, 
    page: number = 1, 
    pageSize: number = 10
  ): Promise<{
    transactions: Transaction[]
    totalCount: number
    totalPages: number
  }> {
    try {
      console.log(`💰 Fetching paginated transactions for user: ${userId}, page: ${page}, pageSize: ${pageSize}`)

      // Calculate offset for pagination
      const offset = (page - 1) * pageSize

      // First, get the total count for this user
      const { count, error: countError } = await this.supabase
        .from('transactions')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)

      if (countError) {
        console.error('❌ Get transaction count error:', countError)
        throw new Error('Failed to fetch transaction count')
      }

      const totalCount = count || 0
      const totalPages = Math.ceil(totalCount / pageSize)

      // Then get the actual transactions for this page
      const { data, error } = await this.supabase
        .from('transactions')
        .select(`
          *,
          orders!transactions_order_id_fkey (
            id,
            status,
            product_name
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + pageSize - 1)

      if (error) {
        console.error('❌ Get paginated user transactions error:', error)
        throw new Error('Failed to fetch transactions')
      }

      console.log(`✅ Fetched ${data.length} transactions for user (page ${page}/${totalPages})`)
      
      return {
        transactions: data.map(this.mapSupabaseTransactionToAppTransaction),
        totalCount,
        totalPages
      }
    } catch (error: any) {
      console.error('❌ Get paginated user transactions failed:', error)
      throw new Error('حدث خطأ أثناء جلب المعاملات')
    }
  }
}
