/**
 * Admin Paginated Users API Route
 * ✅ PERFORMANCE: Server-side pagination with search and filtering for user management
 */

import { NextRequest, NextResponse } from 'next/server'
import { SupabaseAdminService } from '../../../../../lib/services/supabase-admin'
import { createSupabaseServerClient } from '../../../../../lib/supabase-server'

export async function GET(request: NextRequest) {
  try {
    // 🔒 ADMIN AUTHENTICATION
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check admin privileges
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('role, status')
      .eq('id', user.id)
      .single()

    if (profileError || !profile || profile.role !== 'admin' || profile.status !== 'active') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const pageSize = parseInt(searchParams.get('pageSize') || '20')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || 'all'

    // Validate parameters
    if (page < 1 || pageSize < 1 || pageSize > 100) {
      return NextResponse.json(
        { error: 'Invalid page or pageSize parameters' },
        { status: 400 }
      )
    }

    console.log(`📊 Admin: Fetching paginated users - page: ${page}, pageSize: ${pageSize}, search: "${search}", status: ${status}`)

    // ✅ PERFORMANCE: Get paginated results with server-side filtering
    const result = await SupabaseAdminService.getAllUsersPaginated(page, pageSize, search, status)

    console.log(`✅ Admin: Fetched ${result.users.length} users (page ${page}/${result.totalPages})`)

    // Disable caching for real-time updates
    return NextResponse.json({
      users: result.users,
      totalCount: result.totalCount,
      totalPages: result.totalPages,
      currentPage: page,
      pageSize: pageSize,
      hasNextPage: page < result.totalPages,
      hasPreviousPage: page > 1,
      searchTerm: search,
      statusFilter: status
    }, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Content-Type': 'application/json',
      },
    })
  } catch (error: any) {
    console.error('❌ Admin paginated users API error:', error)
    
    // Return empty result instead of error for graceful handling
    return NextResponse.json({
      users: [],
      totalCount: 0,
      totalPages: 0,
      currentPage: 1,
      pageSize: 20,
      hasNextPage: false,
      hasPreviousPage: false,
      searchTerm: '',
      statusFilter: 'all'
    })
  }
} 