'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function BlogCategoryRedirectPage() {
  const router = useRouter()

  useEffect(() => {
    // Immediately redirect to home page
    router.replace('/')
  }, [router])

  // Show loading while redirecting
  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-500 mx-auto mb-4"></div>
        <p className="text-white">جاري التوجيه إلى الصفحة الرئيسية...</p>
        <p className="text-gray-400 text-sm">Redirecting to home page...</p>
      </div>
    </div>
  )
} 