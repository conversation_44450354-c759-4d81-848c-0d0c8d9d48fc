@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 26 26 26;
    --foreground: 255 255 255;
    --primary: 139 38 53;
    --accent: 255 76 76;
    --muted: 42 42 42;
    --border: 58 58 58;
  }

  .dark {
    --background: 26 26 26;
    --foreground: 255 255 255;
    --primary: 139 38 53;
    --accent: 255 76 76;
    --muted: 42 42 42;
    --border: 58 58 58;
  }

  * {
    border-color: hsl(var(--border));
  }

  body {
    background-color: #1a1a1a;
    color: #ffffff;
  }

  html {
    background-color: #1a1a1a;
  }
}

/* Custom scrollbar for dark theme */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #2a2a2a;
}

::-webkit-scrollbar-thumb {
  background: #4a4a4a;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #5a5a5a;
}

/* Custom animations for auth page */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

/* Shimmer effect for selected product cards */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Enhanced card hover and selection animations */
@keyframes cardPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

@keyframes priceReveal {
  0% {
    opacity: 0;
    transform: translateY(10px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Utility classes for animations */
.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

.animate-slideInFromRight {
  animation: slideInFromRight 0.5s ease-out;
}

.animate-slideInFromLeft {
  animation: slideInFromLeft 0.5s ease-out;
}

.animate-scaleIn {
  animation: scaleIn 0.3s ease-out;
}

.animate-bounce-custom {
  animation: bounce 1s ease-in-out;
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-card-pulse {
  animation: cardPulse 0.6s ease-in-out;
}

.animate-slideDown {
  animation: slideDown 0.3s ease-out;
}

.animate-price-reveal {
  animation: priceReveal 0.5s ease-out;
}

/* Enhanced focus states for better UX */
.focus-enhanced:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.3);
  transform: scale(1.02);
}

/* Smooth transitions for all interactive elements */
.transition-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover effects for buttons */
.btn-hover-effect {
  position: relative;
  overflow: hidden;
}

.btn-hover-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.btn-hover-effect:hover::before {
  left: 100%;
}

/* Enhanced text visibility for auth tabs - REVERSED LOGIC */
.auth-tab-active {
  /* This is now for the INACTIVE/NON-SELECTED tab with red background */
  color: white !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
  font-weight: 600;
}

.auth-tab-inactive {
  /* This is now for the ACTIVE/SELECTED tab with normal background */
  color: #ffffff;
  font-weight: 600;
  transition: color 0.3s ease;
}

.auth-tab-inactive:hover {
  color: #f3f4f6;
}
