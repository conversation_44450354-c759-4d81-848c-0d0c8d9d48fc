/**
 * Analytics Service
 * Provides comprehensive analytics for admin dashboard
 */

import { supabaseAdmin } from '../supabase-admin'

export class AnalyticsService {
  private static supabase = supabaseAdmin

  /**
   * Get comprehensive dashboard analytics
   */
  static async getDashboardAnalytics() {
    try {
      // Get basic stats
      const { data: basicStats, error: basicError } = await this.supabase
        .from('enhanced_dashboard_stats')
        .select('*')
        .single()

      if (basicError) {
        return this.getBasicDashboardStats()
      }

      // Get transaction analytics
      const transactionAnalytics = await this.getTransactionAnalytics()
      
      // Get user activity analytics
      const userAnalytics = await this.getUserActivityAnalytics()
      
      // Get revenue trends
      const revenueTrends = await this.getRevenueTrends()

      console.log('✅ Dashboard analytics fetched successfully')
      
      return {
        ...basicStats,
        transactions: transactionAnalytics,
        users: userAnalytics,
        revenue: revenueTrends,
        lastUpdated: new Date().toISOString()
      }
    } catch (error: any) {
      console.error('❌ Get dashboard analytics failed:', error)
      throw new Error('حدث خطأ أثناء جلب تحليلات لوحة التحكم')
    }
  }

  /**
   * Get basic dashboard stats (fallback)
   */
  private static async getBasicDashboardStats() {
    const [users, orders, products, transactions] = await Promise.all([
      this.supabase.from('user_profiles').select('count', { count: 'exact' }),
      this.supabase.from('orders').select('count', { count: 'exact' }),
      this.supabase.from('products').select('count', { count: 'exact' }),
      this.supabase.from('transactions').select('count', { count: 'exact' })
    ])

    return {
      total_users: users.count || 0,
      total_orders: orders.count || 0,
      total_products: products.count || 0,
      total_transactions: transactions.count || 0
    }
  }

  /**
   * Get transaction analytics
   */
  static async getTransactionAnalytics() {
    try {
      const { data, error } = await this.supabase
        .from('transactions')
        .select(`
          type,
          amount,
          created_at,
          admin_id,
          user_profiles!transactions_user_id_fkey(name),
          admin:user_profiles!transactions_admin_id_fkey(name)
        `)
        .order('created_at', { ascending: false })
        .limit(100)

      if (error) throw error

      // Analyze transaction patterns
      const analytics = {
        total_volume: 0,
        by_type: {} as Record<string, { count: number; volume: number }>,
        by_admin: {} as Record<string, { count: number; volume: number }>,
        recent_activity: data?.slice(0, 10) || [],
        daily_trends: this.calculateDailyTrends(data || [])
      }

      data?.forEach(transaction => {
        analytics.total_volume += transaction.amount

        // By type
        if (!analytics.by_type[transaction.type]) {
          analytics.by_type[transaction.type] = { count: 0, volume: 0 }
        }
        analytics.by_type[transaction.type].count++
        analytics.by_type[transaction.type].volume += transaction.amount

        // By admin
        if (transaction.admin_id && Array.isArray(transaction.admin) && transaction.admin.length > 0 && transaction.admin[0]?.name) {
          const adminName = transaction.admin[0].name
          if (!analytics.by_admin[adminName]) {
            analytics.by_admin[adminName] = { count: 0, volume: 0 }
          }
          analytics.by_admin[adminName].count++
          analytics.by_admin[adminName].volume += transaction.amount
        }
      })

      return analytics
    } catch (error) {
      console.error('❌ Transaction analytics error:', error)
      return {
        total_volume: 0,
        by_type: {},
        by_admin: {},
        recent_activity: [],
        daily_trends: []
      }
    }
  }

  /**
   * Get user activity analytics
   */
  static async getUserActivityAnalytics() {
    try {
      const { data, error } = await this.supabase
        .from('user_profiles')
        .select(`
          id,
          name,
          email,
          status,
          balance,
          created_at,
          orders(count),
          transactions(count)
        `)

      if (error) throw error

      const now = new Date()
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)

      const analytics = {
        total_users: data?.length || 0,
        new_users_30d: 0,
        new_users_7d: 0,
        active_users: 0,
        total_balance: 0,
        avg_balance: 0,
        user_distribution: {
          active: 0,
          pending: 0,
          suspended: 0
        }
      }

      data?.forEach(user => {
        const createdAt = new Date(user.created_at)
        
        if (createdAt >= thirtyDaysAgo) analytics.new_users_30d++
        if (createdAt >= sevenDaysAgo) analytics.new_users_7d++
        
        analytics.total_balance += user.balance || 0
        analytics.user_distribution[user.status as keyof typeof analytics.user_distribution]++
        
        // Consider user active if they have orders or transactions
        if ((user.orders as any)?.length > 0 || (user.transactions as any)?.length > 0) {
          analytics.active_users++
        }
      })

      analytics.avg_balance = analytics.total_users > 0 ? analytics.total_balance / analytics.total_users : 0

      return analytics
    } catch (error) {
      console.error('❌ User analytics error:', error)
      return {
        total_users: 0,
        new_users_30d: 0,
        new_users_7d: 0,
        active_users: 0,
        total_balance: 0,
        avg_balance: 0,
        user_distribution: { active: 0, pending: 0, suspended: 0 }
      }
    }
  }

  /**
   * Get revenue trends
   */
  static async getRevenueTrends() {
    try {
      const { data, error } = await this.supabase
        .from('orders')
        .select('price, created_at, status')
        .eq('status', 'completed')
        .order('created_at', { ascending: false })
        .limit(1000)

      if (error) throw error

      const trends = this.calculateRevenueTrends(data || [])
      return trends
    } catch (error) {
      console.error('❌ Revenue trends error:', error)
      return {
        daily: [],
        weekly: [],
        monthly: [],
        total: 0
      }
    }
  }

  /**
   * Calculate daily trends from transaction data
   */
  private static calculateDailyTrends(transactions: any[]) {
    const trends: Record<string, number> = {}
    
    transactions.forEach(transaction => {
      const date = new Date(transaction.created_at).toISOString().split('T')[0]
      trends[date] = (trends[date] || 0) + transaction.amount
    })

    return Object.entries(trends)
      .map(([date, amount]) => ({ date, amount }))
      .sort((a, b) => a.date.localeCompare(b.date))
      .slice(-30) // Last 30 days
  }

  /**
   * Calculate revenue trends
   */
  private static calculateRevenueTrends(orders: any[]) {
    const daily: Record<string, number> = {}
    const weekly: Record<string, number> = {}
    const monthly: Record<string, number> = {}
    let total = 0

    orders.forEach(order => {
      const date = new Date(order.created_at)
      const dayKey = date.toISOString().split('T')[0]
      const weekKey = this.getWeekKey(date)
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`

      daily[dayKey] = (daily[dayKey] || 0) + order.price
      weekly[weekKey] = (weekly[weekKey] || 0) + order.price
      monthly[monthKey] = (monthly[monthKey] || 0) + order.price
      total += order.price
    })

    return {
      daily: Object.entries(daily).map(([date, revenue]) => ({ date, revenue })).slice(-30),
      weekly: Object.entries(weekly).map(([week, revenue]) => ({ week, revenue })).slice(-12),
      monthly: Object.entries(monthly).map(([month, revenue]) => ({ month, revenue })).slice(-12),
      total
    }
  }

  /**
   * Get week key for grouping
   */
  private static getWeekKey(date: Date): string {
    const year = date.getFullYear()
    const week = Math.ceil((date.getTime() - new Date(year, 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000))
    return `${year}-W${String(week).padStart(2, '0')}`
  }

  /**
   * Get top performing products
   */
  static async getTopProducts(limit: number = 10) {
    try {
      const { data, error } = await this.supabase
        .from('orders')
        .select('product_name, price')
        .eq('status', 'completed')
        .order('created_at', { ascending: false })

      if (error) throw error

      // Group and count manually since Supabase doesn't support GROUP BY in this context
      const productCounts: Record<string, { count: number, price: number, name: string }> = {}

      data?.forEach(order => {
        const key = order.product_name
        if (!productCounts[key]) {
          productCounts[key] = { count: 0, price: order.price, name: order.product_name }
        }
        productCounts[key].count++
      })

      return Object.values(productCounts)
        .sort((a, b) => b.count - a.count)
        .slice(0, limit)
    } catch (error) {
      console.error('❌ Top products error:', error)
      return []
    }
  }
}
