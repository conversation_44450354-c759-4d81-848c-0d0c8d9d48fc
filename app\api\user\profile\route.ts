import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase-server'

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 User profile endpoint called')
    
    const supabase = await createSupabaseServerClient()
    
    // Get the current user from session with better error handling
    const { data: { user: authUser }, error: authError } = await supabase.auth.getUser()
    
    if (authError) {
      console.log('❌ Auth error:', authError.message)
      // Provide more specific error messages
      if (authError.message.includes('rate limit')) {
        return NextResponse.json(
          { success: false, error: 'Rate limit reached', code: 'RATE_LIMIT' },
          { status: 429 }
        )
      } else if (authError.message.includes('session')) {
        return NextResponse.json(
          { success: false, error: 'Session expired', code: 'SESSION_EXPIRED' },
          { status: 401 }
        )
      }
      return NextResponse.json(
        { success: false, error: 'Authentication failed', code: 'AUTH_ERROR' },
        { status: 401 }
      )
    }
    
    if (!authUser) {
      console.log('❌ No authenticated user found')
      return NextResponse.json(
        { success: false, error: 'Not authenticated', code: 'NO_USER' },
        { status: 401 }
      )
    }
    
    console.log('🔍 Fetching profile for authenticated user:', authUser.id)
    
    // Get user profile from our user_profiles table
    const { data: userData, error: dbError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', authUser.id)
      .single()
    
    console.log('📊 Profile query result:', { userData, dbError })
    
    if (dbError) {
      console.error('❌ Database user fetch error:', dbError)
      return NextResponse.json(
        { success: false, error: 'Failed to fetch user profile' },
        { status: 500 }
      )
    }
    
    // Map to our User type
    const user = {
      id: userData.id,
      email: userData.email,
      name: userData.name,
      role: userData.role,
      status: userData.status,
      balance: userData.balance,
      createdAt: userData.created_at,
      updatedAt: userData.updated_at,
    }
    
    console.log('✅ User profile fetched successfully')
    return NextResponse.json({
      success: true,
      user
    }, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Content-Type': 'application/json',
      },
    })
    
  } catch (error) {
    console.error('❌ User profile endpoint error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    console.log('📝 User profile update endpoint called')
    
    const supabase = await createSupabaseServerClient()
    
    // Get the current user from session
    const { data: { user: authUser }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !authUser) {
      console.log('❌ No authenticated user found:', authError)
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      )
    }
    
    // Get the update data from request body
    const { name } = await request.json()
    
    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      return NextResponse.json(
        { success: false, error: 'Name is required and cannot be empty' },
        { status: 400 }
      )
    }
    
    console.log('📝 Updating profile for user:', authUser.id, 'with name:', name)
    
    // Update user profile in our user_profiles table
    const { data: userData, error: dbError } = await supabase
      .from('user_profiles')
      .update({
        name: name.trim(),
        updated_at: new Date().toISOString()
      })
      .eq('id', authUser.id)
      .select()
      .single()
    
    if (dbError) {
      console.error('❌ Database user update error:', dbError)
      return NextResponse.json(
        { success: false, error: 'Failed to update user profile' },
        { status: 500 }
      )
    }
    
    // Map to our User type
    const user = {
      id: userData.id,
      email: userData.email,
      name: userData.name,
      role: userData.role,
      status: userData.status,
      balance: userData.balance,
      createdAt: userData.created_at,
      updatedAt: userData.updated_at,
    }
    
    console.log('✅ User profile updated successfully')
    return NextResponse.json({
      success: true,
      user,
      message: 'Profile updated successfully'
    }, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Content-Type': 'application/json',
      },
    })
    
  } catch (error) {
    console.error('❌ User profile update endpoint error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
} 