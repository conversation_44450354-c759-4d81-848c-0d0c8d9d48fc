/**
 * Admin Paginated Transactions API Route
 * ✅ PERFORMANCE: Server-side pagination for admin transaction management
 */

import { NextRequest, NextResponse } from 'next/server'
import { TransactionService } from '../../../../../lib/services/transactions'
import { createSupabaseServerClient } from '../../../../../lib/supabase-server'

export async function GET(request: NextRequest) {
  try {
    // 🔒 ADMIN AUTHENTICATION
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check admin privileges
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('role, status')
      .eq('id', user.id)
      .single()

    if (profileError || !profile || profile.role !== 'admin' || profile.status !== 'active') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const pageSize = parseInt(searchParams.get('pageSize') || '20')

    // Validate parameters
    if (page < 1 || pageSize < 1 || pageSize > 100) {
      return NextResponse.json(
        { error: 'Invalid page or pageSize parameters' },
        { status: 400 }
      )
    }

    console.log(`📊 Admin: Fetching paginated transactions - page: ${page}, pageSize: ${pageSize}`)

    // ✅ PERFORMANCE: Get paginated results with total count
    const result = await TransactionService.getAllTransactionsPaginated(page, pageSize)

    console.log(`✅ Admin: Fetched ${result.transactions.length} transactions (page ${page}/${result.totalPages})`)

    return NextResponse.json({
      transactions: result.transactions,
      totalCount: result.totalCount,
      totalPages: result.totalPages,
      currentPage: page,
      pageSize: pageSize,
      hasNextPage: page < result.totalPages,
      hasPreviousPage: page > 1
    }, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate', // No caching for real-time data
        'Pragma': 'no-cache',
        'Expires': '0',
        'Content-Type': 'application/json',
      },
    })
  } catch (error: any) {
    console.error('❌ Admin paginated transactions API error:', error)
    
    // Return empty result instead of error for graceful handling
    return NextResponse.json({
      transactions: [],
      totalCount: 0,
      totalPages: 0,
      currentPage: 1,
      pageSize: 20,
      hasNextPage: false,
      hasPreviousPage: false
    }, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Content-Type': 'application/json',
      },
    })
  }
} 