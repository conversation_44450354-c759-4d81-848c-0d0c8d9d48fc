'use client'

import { useState, useEffect, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { Eye, EyeOff, ArrowRight, LogIn, UserPlus, Shield, AlertCircle, CheckCircle, XCircle, AlertTriangle, RefreshCw } from 'lucide-react'
import { useSupabaseAuth } from '../../contexts/SupabaseAuthContext'
import { ButtonLoading } from '../components/LoadingSpinner'

type AuthMode = 'login' | 'signup'

// Enhanced error mapping with emojis and Arabic messages
const getErrorMessage = (error: string): { message: string; emoji: string; type: 'error' | 'warning' | 'info' } => {
  const errorLower = error.toLowerCase()
  
  // Authentication specific errors
  if (errorLower.includes('invalid login credentials') || errorLower.includes('invalid credentials')) {
    return { message: 'البريد الإلكتروني أو كلمة المرور غير صحيحة', emoji: '🔐', type: 'error' }
  }
  
  if (errorLower.includes('email not confirmed')) {
    return { message: 'يرجى تأكيد بريدك الإلكتروني قبل تسجيل الدخول', emoji: '📧', type: 'warning' }
  }
  
  if (errorLower.includes('too many requests') || errorLower.includes('rate limit')) {
    return { message: 'تم تجاوز عدد المحاولات المسموح، يرجى المحاولة بعد قليل', emoji: '⏰', type: 'warning' }
  }
  
  if (errorLower.includes('user already registered') || errorLower.includes('already exists')) {
    return { message: 'يوجد حساب مسجل بهذا البريد الإلكتروني بالفعل', emoji: '👤', type: 'warning' }
  }
  
  if (errorLower.includes('weak password') || errorLower.includes('password should be')) {
    return { message: 'كلمة المرور ضعيفة، يرجى اختيار كلمة مرور أقوى', emoji: '🔒', type: 'warning' }
  }
  
  if (errorLower.includes('network') || errorLower.includes('connection')) {
    return { message: 'مشكلة في الاتصال، يرجى التحقق من الإنترنت والمحاولة مرة أخرى', emoji: '🌐', type: 'error' }
  }
  
  if (errorLower.includes('invalid email') || errorLower.includes('email format')) {
    return { message: 'تنسيق البريد الإلكتروني غير صحيح', emoji: '📮', type: 'error' }
  }
  
  if (errorLower.includes('signup disabled') || errorLower.includes('registration') || errorLower.includes('التسجيل غير متاح')) {
    return { message: 'التسجيل غير متاح حالياً، يرجى التواصل مع الدعم الفني', emoji: '🚫', type: 'warning' }
  }
  
  if (errorLower.includes('account suspended') || errorLower.includes('suspended')) {
    return { message: 'تم إيقاف حسابك، يرجى التواصل مع الدعم الفني', emoji: '⛔', type: 'error' }
  }
  
  if (errorLower.includes('account pending') || errorLower.includes('pending')) {
    return { message: 'حسابك في انتظار الموافقة، سيتم إشعارك عند التفعيل', emoji: '⏳', type: 'info' }
  }
  
  // Generic fallback
  return { message: 'حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى', emoji: '❌', type: 'error' }
}

// Enhanced notification component
const ErrorNotification = ({ error, onClose }: { error: string; onClose?: () => void }) => {
  const { message, emoji, type } = getErrorMessage(error)
  
  const bgColor = {
    error: 'bg-red-500/10 border-red-500/20',
    warning: 'bg-yellow-500/10 border-yellow-500/20',
    info: 'bg-blue-500/10 border-blue-500/20'
  }[type]
  
  const textColor = {
    error: 'text-red-400',
    warning: 'text-yellow-400',
    info: 'text-blue-400'
  }[type]
  
  const Icon = {
    error: XCircle,
    warning: AlertTriangle,
    info: AlertCircle
  }[type]
  
  return (
    <div className={`mb-4 ${bgColor} border rounded-lg p-4 animate-slideDown`}>
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0 mt-0.5">
          <div className="text-lg">{emoji}</div>
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div>
              <p className={`${textColor} text-sm font-medium mb-1`}>
                {type === 'error' ? 'خطأ في العملية' : type === 'warning' ? 'تنبيه مهم' : 'معلومة'}
              </p>
              <p className="text-gray-300 text-sm leading-relaxed">
                {message}
              </p>
            </div>
            {onClose && (
              <button
                onClick={onClose}
                className="flex-shrink-0 ml-2 text-gray-400 hover:text-white transition-colors"
              >
                <XCircle className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

// Success notification component
const SuccessNotification = ({ message }: { message: string }) => (
  <div className="mb-4 bg-green-500/10 border border-green-500/20 rounded-lg p-4 animate-slideDown">
    <div className="flex items-start gap-3">
      <div className="flex-shrink-0 mt-0.5">
        <div className="text-lg">✅</div>
      </div>
      <div className="flex-1">
        <p className="text-green-400 text-sm font-medium mb-1">
          تم بنجاح
        </p>
        <p className="text-gray-300 text-sm">
          {message}
        </p>
      </div>
    </div>
  </div>
)

// Session cleanup utility
const clearAuthSession = async () => {
  try {
    // Clear localStorage
    localStorage.removeItem('neran_user')
    localStorage.removeItem('neran_user_timestamp')
    localStorage.removeItem('sb-' + process.env.NEXT_PUBLIC_SUPABASE_URL?.split('//')[1]?.split('.')[0] + '-auth-token')
    
    // Clear sessionStorage
    sessionStorage.clear()
    
    // Clear any Supabase auth cookies by setting them to expire
    const cookiesToClear = [
      'sb-access-token',
      'sb-refresh-token',
      'supabase-auth-token',
      'supabase.auth.token'
    ]
    
    cookiesToClear.forEach(cookieName => {
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${window.location.hostname};`
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`
    })
    
    console.log('🧹 Session cleanup completed')
    return true
  } catch (error) {
    console.error('❌ Session cleanup error:', error)
    return false
  }
}

function AuthPageContent() {
  const searchParams = useSearchParams()
  const [mode, setMode] = useState<AuthMode>('login')
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [successMessage, setSuccessMessage] = useState('')
  const [sessionCleared, setSessionCleared] = useState(false)
  const [loginData, setLoginData] = useState({
    email: '',
    password: '',
  })
  const [signupData, setSignupData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [allowRegistrations, setAllowRegistrations] = useState(true)

  const { signIn, signUp } = useSupabaseAuth()
  const router = useRouter()

  // Set initial mode based on URL parameter
  useEffect(() => {
    const modeParam = searchParams.get('mode')
    if (modeParam === 'signup' || modeParam === 'login') {
      setMode(modeParam as AuthMode)
    }
  }, [searchParams])

  // Get redirect parameter for display
  const redirectParam = searchParams.get('redirect')

  // Auto-clear session on mount if there are auth issues
  useEffect(() => {
    const handleSessionIssues = async () => {
      // Check for session issues in URL params
      const sessionIssue = searchParams.get('session_expired') || searchParams.get('auth_error')
      
      if (sessionIssue && !sessionCleared) {
        console.log('🔄 Detected session issue, clearing auth data...')
        await clearAuthSession()
        setSessionCleared(true)
        setSuccessMessage('تم تنظيف بيانات الجلسة، يمكنك الآن تسجيل الدخول مرة أخرى 🔄')
        
        // Clean URL
        const newUrl = new URL(window.location.href)
        newUrl.searchParams.delete('session_expired')
        newUrl.searchParams.delete('auth_error')
        window.history.replaceState({}, '', newUrl.toString())
      }
    }

    handleSessionIssues()
  }, [searchParams, sessionCleared])

  // Check if registrations are allowed
  useEffect(() => {
    const checkRegistrationSettings = async () => {
      try {
        const response = await fetch('/api/settings')
        if (response.ok) {
          const settings = await response.json()
          setAllowRegistrations(settings.allowRegistrations !== false)
        }
      } catch (error) {
        console.warn('Failed to fetch registration settings:', error)
        // Default to allowing registrations if fetch fails
      }
    }

    checkRegistrationSettings()
  }, [])

  const validateLoginForm = () => {
    const newErrors: Record<string, string> = {}

    if (!loginData.email) {
      newErrors.email = '📧 البريد الإلكتروني مطلوب'
    } else if (!/\S+@\S+\.\S+/.test(loginData.email)) {
      newErrors.email = '📮 البريد الإلكتروني غير صحيح'
    }

    if (!loginData.password) {
      newErrors.password = '🔐 كلمة المرور مطلوبة'
    } else if (loginData.password.length < 3) {
      newErrors.password = '🔒 كلمة المرور قصيرة جداً'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const validateSignupForm = () => {
    const newErrors: Record<string, string> = {}

    if (!signupData.name) {
      newErrors.name = '👤 الاسم مطلوب'
    } else if (signupData.name.length < 2) {
      newErrors.name = '📝 الاسم قصير جداً'
    }

    if (!signupData.email) {
      newErrors.email = '📧 البريد الإلكتروني مطلوب'
    } else if (!/\S+@\S+\.\S+/.test(signupData.email)) {
      newErrors.email = '📮 البريد الإلكتروني غير صحيح'
    }

    if (!signupData.password) {
      newErrors.password = '🔐 كلمة المرور مطلوبة'
    } else if (signupData.password.length < 6) {
      newErrors.password = '🔒 كلمة المرور يجب أن تكون 6 أحرف على الأقل'
    }

    if (!signupData.confirmPassword) {
      newErrors.confirmPassword = '🔁 تأكيد كلمة المرور مطلوب'
    } else if (signupData.password !== signupData.confirmPassword) {
      newErrors.confirmPassword = '❌ كلمة المرور غير متطابقة'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateLoginForm()) return

    setLoading(true)
    setErrors({})
    setSuccessMessage('')
    
    try {
      // Clear any stale session data before login
      if (!sessionCleared) {
        await clearAuthSession()
        setSessionCleared(true)
      }
      
      await signIn(loginData.email, loginData.password)
      
      // Show success message briefly
      setSuccessMessage('تم تسجيل الدخول بنجاح! 🎉')

      // Give a small delay for the auth state to update
      setTimeout(() => {
        // Check for redirect parameter and redirect accordingly
        const redirectParam = searchParams.get('redirect')
        if (redirectParam) {
          // Decode the redirect parameter in case it's URL encoded
          const decodedRedirect = decodeURIComponent(redirectParam)
          console.log('🔄 Redirecting to:', decodedRedirect)
          router.replace(decodedRedirect) // Use replace to avoid back button issues
        } else {
          router.replace('/') // Use replace instead of push
        }
        setLoading(false) // Set loading to false after redirect
      }, 1000) // Show success message for 1 second

    } catch (error: any) {
      setErrors({ submit: error.message })
      setLoading(false)
    }
  }

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateSignupForm()) return

    setLoading(true)
    setErrors({})
    setSuccessMessage('')
    
    try {
      await signUp(signupData.email, signupData.password, signupData.name)
      
      // Show success message
      setSuccessMessage('تم إنشاء الحساب بنجاح! سيتم توجيهك الآن... 🎉')
      
      // Delay redirect to show success message
      setTimeout(() => {
        router.push('/signup/success')
      }, 1500)
    } catch (error: any) {
      setErrors({ submit: error.message })
    } finally {
      setLoading(false)
    }
  }

  const switchMode = (newMode: AuthMode) => {
    // Add a small delay for smooth animation
    setErrors({})
    setSuccessMessage('')
    setShowPassword(false)
    setShowConfirmPassword(false)

    // Clear form data when switching modes
    if (newMode === 'login') {
      setSignupData({ name: '', email: '', password: '', confirmPassword: '' })
    } else {
      setLoginData({ email: '', password: '' })
    }

    setTimeout(() => {
      setMode(newMode)
    }, 50)
  }

  return (
    <div className="min-h-screen flex items-center justify-center px-4 py-8 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-red-800/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-red-600/3 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-red-700/10 rounded-full blur-2xl animate-bounce-custom" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="w-full max-w-md animate-scaleIn relative z-10">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2 transition-all duration-500 ease-in-out">
            {mode === 'login' ? '🔐 تسجيل الدخول' : '👤 إنشاء حساب جديد'}
          </h1>
          <p className="text-gray-400 transition-all duration-500 ease-in-out">
            {mode === 'login' ? '🔥 مرحباً بك في نيران كارد' : '🎮 انضم إلى نيران كارد اليوم'}
          </p>
          {redirectParam === '/admin' && (
            <div className="mt-4 px-4 py-3 bg-red-900/20 border border-red-700/30 rounded-lg">
              <div className="flex items-center justify-center gap-2 text-red-300">
                <Shield className="w-4 h-4" />
                <span className="text-sm font-medium">👑 مطلوب تسجيل الدخول للوصول إلى لوحة التحكم</span>
              </div>
            </div>
          )}
        </div>

        {/* Mode Tabs */}
        <div className="flex bg-gray-800 rounded-lg p-1 mb-6 border border-gray-600 relative overflow-hidden">
          {/* Animated Background - Now shows on INACTIVE tab */}
          <div
            className={`absolute top-1 bottom-1 bg-red-700 rounded-md transition-all duration-300 ease-in-out shadow-lg ${
              mode === 'signup' ? 'left-1 right-1/2 mr-0.5' : 'right-1 left-1/2 ml-0.5'
            }`}
            style={{
              background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',
              boxShadow: '0 2px 8px rgba(220, 38, 38, 0.3)'
            }}
          />

          <button
            onClick={() => switchMode('login')}
            className={`flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-md transition-all duration-300 relative z-10 ${
              mode === 'login'
                ? 'auth-tab-inactive'
                : 'auth-tab-active'
            }`}
          >
            <LogIn className={`w-4 h-4 transition-transform duration-300 ${mode !== 'login' ? 'scale-110' : ''}`} />
            تسجيل الدخول
          </button>
          <button
            onClick={() => allowRegistrations && switchMode('signup')}
            disabled={!allowRegistrations}
            className={`flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-md transition-all duration-300 relative z-10 ${
              !allowRegistrations
                ? 'opacity-50 cursor-not-allowed text-gray-500'
                : mode === 'signup'
                ? 'auth-tab-inactive'
                : 'auth-tab-active'
            }`}
            title={!allowRegistrations ? 'التسجيل غير متاح حالياً' : ''}
          >
            <UserPlus className={`w-4 h-4 transition-transform duration-300 ${mode !== 'signup' && allowRegistrations ? 'scale-110' : ''}`} />
            إنشاء حساب
            {!allowRegistrations && <span className="text-xs">(غير متاح)</span>}
          </button>
        </div>

        {/* Session Cleanup Button */}
        {!sessionCleared && (
          <div className="mb-4 text-center">
            <button
              onClick={async () => {
                await clearAuthSession()
                setSessionCleared(true)
                setSuccessMessage('تم تنظيف بيانات الجلسة بنجاح! 🧹')
              }}
              className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors"
            >
              <RefreshCw className="w-4 h-4" />
              🧹 تنظيف بيانات الجلسة
            </button>
            <p className="text-gray-400 text-xs mt-1">
              إذا كنت تواجه مشاكل في تسجيل الدخول
            </p>
          </div>
        )}

        {/* Form Container */}
        <div className="bg-gray-800/90 backdrop-blur-sm rounded-xl p-6 border border-gray-600/50 overflow-hidden shadow-2xl">
          {/* Success Message */}
          {successMessage && (
            <SuccessNotification message={successMessage} />
          )}

          <div className="relative min-h-[400px]">
            {/* Login Form */}
            <div className={`transition-all duration-500 ease-in-out ${
              mode === 'login'
                ? 'opacity-100 translate-x-0 pointer-events-auto'
                : 'opacity-0 translate-x-full pointer-events-none absolute inset-0'
            }`}>
              <form onSubmit={handleLogin} className="space-y-4">
                {/* Email Field */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    📧 البريد الإلكتروني
                  </label>
                  <input
                    type="email"
                    value={loginData.email}
                    onChange={(e) => setLoginData({ ...loginData, email: e.target.value })}
                    className={`w-full px-4 py-3 bg-gray-900 border rounded-lg text-white focus:outline-none focus:ring-2 transition-all duration-300 ${
                      errors.email ? 'border-red-500 focus:ring-red-500' : 'border-gray-600 focus:ring-red-800'
                    }`}
                    placeholder="أدخل بريدك الإلكتروني"
                  />
                  {errors.email && (
                    <p className="text-red-400 text-sm mt-1 animate-slideDown">{errors.email}</p>
                  )}
                </div>

                {/* Password Field */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    🔐 كلمة المرور
                  </label>
                  <div className="relative">
                    <input
                      type={showPassword ? 'text' : 'password'}
                      value={loginData.password}
                      onChange={(e) => setLoginData({ ...loginData, password: e.target.value })}
                      className={`w-full px-4 py-3 pr-12 bg-gray-900 border rounded-lg text-white focus:outline-none focus:ring-2 transition-all duration-300 ${
                        errors.password ? 'border-red-500 focus:ring-red-500' : 'border-gray-600 focus:ring-red-800'
                      }`}
                      placeholder="أدخل كلمة المرور"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors duration-200"
                    >
                      {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>
                  </div>
                  {errors.password && (
                    <p className="text-red-400 text-sm mt-1 animate-slideDown">{errors.password}</p>
                  )}
                </div>

                {/* Submit Error */}
                {errors.submit && (
                  <ErrorNotification 
                    error={errors.submit} 
                    onClose={() => setErrors({ ...errors, submit: '' })}
                  />
                )}

                {/* Submit Button */}
                <button
                  type="submit"
                  disabled={loading}
                  className="w-full bg-red-800 hover:bg-red-900 disabled:opacity-50 disabled:cursor-not-allowed text-white py-3 rounded-lg transition-all duration-300 font-medium mb-4 transform hover:scale-95 active:scale-90 btn-hover-effect shadow-lg hover:shadow-red-800/25"
                >
                  {loading ? (
                    <div className="flex items-center justify-center gap-2">
                      <ButtonLoading />
                      ⏳ جاري تسجيل الدخول...
                    </div>
                  ) : (
                    '🚀 تسجيل الدخول'
                  )}
                </button>
              </form>
            </div>

            {/* Signup Form */}
            <div className={`transition-all duration-500 ease-in-out ${
              mode === 'signup'
                ? 'opacity-100 translate-x-0 pointer-events-auto'
                : 'opacity-0 -translate-x-full pointer-events-none absolute inset-0'
            }`}>
              {!allowRegistrations ? (
                <div className="text-center py-8">
                  <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-6 mb-4">
                    <div className="text-yellow-400 text-lg font-semibold mb-2">
                      🚫 التسجيل غير متاح حالياً
                    </div>
                    <p className="text-gray-300 text-sm">
                      نعتذر، التسجيل معطل مؤقتاً. يرجى المحاولة لاحقاً أو التواصل مع الدعم الفني.
                    </p>
                  </div>
                  <button
                    type="button"
                    onClick={() => switchMode('login')}
                    className="bg-red-800 hover:bg-red-900 text-white px-6 py-2 rounded-lg transition-colors"
                  >
                    🔄 تسجيل الدخول بدلاً من ذلك
                  </button>
                </div>
              ) : (
                <form onSubmit={handleSignup} className="space-y-4">
                {/* Name Field */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    👤 الاسم الكامل
                  </label>
                  <input
                    type="text"
                    value={signupData.name}
                    onChange={(e) => setSignupData({ ...signupData, name: e.target.value })}
                    className={`w-full px-4 py-3 bg-gray-900 border rounded-lg text-white focus:outline-none focus:ring-2 transition-all duration-300 ${
                      errors.name ? 'border-red-500 focus:ring-red-500' : 'border-gray-600 focus:ring-red-800'
                    }`}
                    placeholder="أدخل اسمك الكامل"
                  />
                  {errors.name && (
                    <p className="text-red-400 text-sm mt-1 animate-slideDown">{errors.name}</p>
                  )}
                </div>

                {/* Email Field */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    📧 البريد الإلكتروني
                  </label>
                  <input
                    type="email"
                    value={signupData.email}
                    onChange={(e) => setSignupData({ ...signupData, email: e.target.value })}
                    className={`w-full px-4 py-3 bg-gray-900 border rounded-lg text-white focus:outline-none focus:ring-2 transition-all duration-300 ${
                      errors.email ? 'border-red-500 focus:ring-red-500' : 'border-gray-600 focus:ring-red-800'
                    }`}
                    placeholder="أدخل بريدك الإلكتروني"
                  />
                  {errors.email && (
                    <p className="text-red-400 text-sm mt-1 animate-slideDown">{errors.email}</p>
                  )}
                </div>

                {/* Password Field */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    🔐 كلمة المرور
                  </label>
                  <div className="relative">
                    <input
                      type={showPassword ? 'text' : 'password'}
                      value={signupData.password}
                      onChange={(e) => setSignupData({ ...signupData, password: e.target.value })}
                      className={`w-full px-4 py-3 pr-12 bg-gray-900 border rounded-lg text-white focus:outline-none focus:ring-2 transition-all duration-300 ${
                        errors.password ? 'border-red-500 focus:ring-red-500' : 'border-gray-600 focus:ring-red-800'
                      }`}
                      placeholder="أدخل كلمة المرور"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors duration-200"
                    >
                      {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>
                  </div>
                  {errors.password && (
                    <p className="text-red-400 text-sm mt-1 animate-slideDown">{errors.password}</p>
                  )}
                </div>

                {/* Confirm Password Field */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    🔁 تأكيد كلمة المرور
                  </label>
                  <div className="relative">
                    <input
                      type={showConfirmPassword ? 'text' : 'password'}
                      value={signupData.confirmPassword}
                      onChange={(e) => setSignupData({ ...signupData, confirmPassword: e.target.value })}
                      className={`w-full px-4 py-3 pr-12 bg-gray-900 border rounded-lg text-white focus:outline-none focus:ring-2 transition-all duration-300 ${
                        errors.confirmPassword ? 'border-red-500 focus:ring-red-500' : 'border-gray-600 focus:ring-red-800'
                      }`}
                      placeholder="أعد إدخال كلمة المرور"
                    />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors duration-200"
                    >
                      {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>
                  </div>
                  {errors.confirmPassword && (
                    <p className="text-red-400 text-sm mt-1 animate-slideDown">{errors.confirmPassword}</p>
                  )}
                </div>

                {/* Submit Error */}
                {errors.submit && (
                  <ErrorNotification 
                    error={errors.submit} 
                    onClose={() => setErrors({ ...errors, submit: '' })}
                  />
                )}

                {/* Submit Button */}
                <button
                  type="submit"
                  disabled={loading}
                  className="w-full bg-red-800 hover:bg-red-900 disabled:opacity-50 disabled:cursor-not-allowed text-white py-3 rounded-lg transition-all duration-300 font-medium mb-4 transform hover:scale-95 active:scale-90 btn-hover-effect shadow-lg hover:shadow-red-800/25"
                >
                  {loading ? (
                    <div className="flex items-center justify-center gap-2">
                      <ButtonLoading />
                      ⏳ جاري إنشاء الحساب...
                    </div>
                  ) : (
                    '🎯 إنشاء حساب جديد'
                  )}
                </button>
              </form>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <Link 
            href="/" 
            className="inline-flex items-center gap-2 text-gray-400 hover:text-white transition-colors duration-300 group"
          >
            <ArrowRight className="w-4 h-4 transition-transform duration-300 group-hover:-translate-x-1" />
            🏠 العودة إلى الصفحة الرئيسية
          </Link>
        </div>
      </div>
    </div>
  )
}

export default function AuthPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <ButtonLoading />
      </div>
    }>
      <AuthPageContent />
    </Suspense>
  )
}
