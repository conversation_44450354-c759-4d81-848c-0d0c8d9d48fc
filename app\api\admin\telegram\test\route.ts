import { NextRequest, NextResponse } from 'next/server'
import { TelegramBotService } from '../../../../../lib/services/telegram-bot'
import { createSupabaseServerClient } from '../../../../../lib/supabase-server'

export async function POST(request: NextRequest) {
  try {
    // 🔒 SECURITY: Verify admin access
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get user profile to check admin role
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    console.log('🤖 Testing Telegram bot configuration...')

    // Check environment variables
    const botToken = process.env.TELEGRAM_BOT_TOKEN
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL
    const vercelUrl = process.env.VERCEL_URL
    const vercelProjectUrl = process.env.VERCEL_PROJECT_PRODUCTION_URL
    const adminChatId = process.env.TELEGRAM_ADMIN_CHAT_ID
    const adminChatIds = process.env.TELEGRAM_ADMIN_CHAT_IDS

    // Show dynamic URL detection
    const detectedUrl = baseUrl || 
                       (vercelUrl ? `https://${vercelUrl}` : null) || 
                       (vercelProjectUrl ? `https://${vercelProjectUrl}` : null) || 
                       'FALLBACK URL'

    const configStatus = {
      botToken: !!botToken,
      baseUrl: !!baseUrl,
      detectedUrl: detectedUrl,
      vercelUrl: vercelUrl || 'NOT SET',
      vercelProjectUrl: vercelProjectUrl || 'NOT SET',
      adminChatId: !!adminChatId,
      adminChatIds: !!adminChatIds,
      botTokenPreview: botToken ? `${botToken.substring(0, 10)}...` : 'NOT SET',
      baseUrlValue: baseUrl || 'AUTO-DETECTED',
      adminChatIdValue: adminChatId || 'NOT SET',
      adminChatIdsValue: adminChatIds || 'NOT SET',
      urlDetectionInfo: {
        priority1: 'NEXT_PUBLIC_BASE_URL (manual override)',
        priority2: 'VERCEL_URL (auto-detected deployment)',
        priority3: 'VERCEL_PROJECT_PRODUCTION_URL (auto-detected project)',
        priority4: 'Fallback hardcoded URL'
      }
    }

    console.log('🔧 Telegram config status:', configStatus)

    if (!botToken) {
      return NextResponse.json({
        success: false,
        error: 'TELEGRAM_BOT_TOKEN not configured',
        config: configStatus,
        instructions: {
          step1: 'Create a bot by messaging @BotFather on Telegram',
          step2: 'Send /newbot and follow instructions',
          step3: 'Copy the token and add to Vercel environment variables',
          step4: 'Add TELEGRAM_BOT_TOKEN to your .env file'
        }
      }, { status: 400 })
    }

    // Send test notification
    const testOrderData = {
      id: 'test-' + Date.now(),
      user_name: 'اختبار التلقائي',
      user_email: '<EMAIL>',
      product_name: '110 جوهرة - Free Fire',
      game_id: '123456789',
      amount: '110 جوهرة',
      price: 2.25,
      status: 'pending',
      created_at: new Date().toISOString()
    }

    console.log('📤 Sending test notification...')
    const notificationSent = await TelegramBotService.sendOrderNotification(testOrderData)

    if (notificationSent) {
      console.log('✅ Test notification sent successfully!')
      return NextResponse.json({
        success: true,
        message: 'Test notification sent successfully!',
        config: configStatus,
        testData: testOrderData
      })
    } else {
      console.log('❌ Test notification failed')
      return NextResponse.json({
        success: false,
        error: 'Failed to send test notification',
        config: configStatus,
        troubleshooting: {
          check1: 'Verify bot token is correct',
          check2: 'Make sure bot is started (send /start to your bot)',
          check3: 'Verify chat ID is correct',
          check4: 'Check if bot has permissions to send messages'
        }
      }, { status: 500 })
    }

  } catch (error: any) {
    console.error('❌ Telegram test error:', error)
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    // 🔒 SECURITY: Verify admin access
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get user profile to check admin role
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    // Return configuration status
    const botToken = process.env.TELEGRAM_BOT_TOKEN
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL
    const vercelUrl = process.env.VERCEL_URL
    const vercelProjectUrl = process.env.VERCEL_PROJECT_PRODUCTION_URL
    const adminChatId = process.env.TELEGRAM_ADMIN_CHAT_ID
    const adminChatIds = process.env.TELEGRAM_ADMIN_CHAT_IDS

    const detectedUrl = baseUrl || 
                       (vercelUrl ? `https://${vercelUrl}` : null) || 
                       (vercelProjectUrl ? `https://${vercelProjectUrl}` : null) || 
                       'FALLBACK URL'

    return NextResponse.json({
      configuration: {
        botToken: !!botToken,
        baseUrl: !!baseUrl,
        detectedUrl: detectedUrl,
        vercelUrl: vercelUrl || 'NOT SET',
        vercelProjectUrl: vercelProjectUrl || 'NOT SET',
        adminChatId: !!adminChatId,
        adminChatIds: !!adminChatIds,
        botTokenPreview: botToken ? `${botToken.substring(0, 10)}...` : 'NOT SET',
        baseUrlValue: baseUrl || 'AUTO-DETECTED',
        adminChatIdValue: adminChatId || 'NOT SET',
        adminChatIdsValue: adminChatIds || 'NOT SET'
      },
      instructions: {
        setupBot: {
          step1: 'Message @BotFather on Telegram',
          step2: 'Send /newbot command',
          step3: 'Follow instructions to create your bot',
          step4: 'Copy the bot token'
        },
        getChatId: {
          step1: 'Start your bot by sending /start',
          step2: 'Send a message to your bot',
          step3: 'Visit https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates',
          step4: 'Find your chat ID in the response'
        },
        deployConfig: {
          step1: 'Add TELEGRAM_BOT_TOKEN to Vercel environment variables',
          step2: 'Add TELEGRAM_ADMIN_CHAT_ID to Vercel environment variables',
          step3: 'Add NEXT_PUBLIC_BASE_URL with your production domain',
          step4: 'Redeploy the application'
        }
      }
    })

  } catch (error: any) {
    console.error('❌ Telegram config check error:', error)
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 })
  }
} 