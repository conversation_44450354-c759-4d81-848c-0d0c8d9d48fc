/**
 * Date utility functions for consistent date formatting across the application
 * Uses Gregorian calendar instead of Arabic calendar
 */

export const formatDate = (dateString: string | Date, options?: {
  includeTime?: boolean
  relative?: boolean
  short?: boolean
}) => {
  const date = new Date(dateString)
  const { includeTime = false, relative = false, short = false } = options || {}

  // For relative dates (Today, Yesterday)
  if (relative) {
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)
    
    // Check if it's today
    if (date.toDateString() === today.toDateString()) {
      const time = date.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
      })
      return `اليوم ${time}`
    }
    
    // Check if it's yesterday
    if (date.toDateString() === yesterday.toDateString()) {
      const time = date.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
      })
      return `أمس ${time}`
    }
  }

  // Standard date formatting using Gregorian calendar
  const dateOptions: Intl.DateTimeFormatOptions = {
    year: short ? '2-digit' : 'numeric',
    month: short ? 'short' : 'long',
    day: 'numeric',
    calendar: 'gregory' // Force Gregorian calendar
  }

  if (includeTime) {
    dateOptions.hour = '2-digit'
    dateOptions.minute = '2-digit'
    dateOptions.hour12 = false
  }

  return date.toLocaleDateString('en-US', dateOptions)
}

export const formatDateTime = (dateString: string | Date) => {
  return formatDate(dateString, { includeTime: true })
}

export const formatDateShort = (dateString: string | Date) => {
  return formatDate(dateString, { short: true })
}

export const formatDateRelative = (dateString: string | Date) => {
  return formatDate(dateString, { relative: true, includeTime: true })
}

// For admin tables - split date and time
export const formatDateForTable = (dateString: string | Date) => {
  const date = new Date(dateString)
  
  return {
    date: date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      calendar: 'gregory'
    }),
    time: date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    })
  }
}

// For order creation and database storage
export const getCurrentTimestamp = () => {
  return new Date().toISOString()
}

// Convert any existing Arabic dates to Gregorian
export const normalizeDate = (dateString: string | Date) => {
  const date = new Date(dateString)
  return date.toISOString()
}
