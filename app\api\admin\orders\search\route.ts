/**
 * ✅ COMPREHENSIVE ORDER SEARCH API
 * Enhanced search that can find orders by any field including user-based searches
 */

import { NextRequest, NextResponse } from 'next/server'
import { SupabaseAdminService } from '../../../../../lib/services/supabase-admin'
import { createSupabaseServerClient } from '../../../../../lib/supabase-server'

export async function GET(request: NextRequest) {
  try {
    // 🔒 ADMIN AUTHENTICATION
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check admin privileges
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('role, status')
      .eq('id', user.id)
      .single()

    if (profileError || !profile || profile.role !== 'admin' || profile.status !== 'active') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q') || ''
    const searchType = searchParams.get('type') || 'all' // 'all', 'user', 'order'

    if (!query.trim()) {
      return NextResponse.json({
        results: [],
        searchType: 'empty',
        message: 'Please enter a search term'
      })
    }

    console.log(`🔍 Admin comprehensive search - query: "${query}", type: ${searchType}`)

    let results = []

    if (searchType === 'all' || searchType === 'user') {
      // 📋 Search for orders by user (username, email, or user ID)
      const { data: userOrders } = await supabase
        .from('orders')
        .select(`
          *,
          user_profiles:user_id (
            id,
            name,
            email,
            game_id
          ),
          products:product_id (
            id,
            name,
            category,
            subcategory
          )
        `)
        .or(`
          user_profiles.name.ilike.%${query}%,
          user_profiles.email.ilike.%${query}%,
          user_id.eq.${query}
        `.replace(/\s+/g, ''))
        .order('created_at', { ascending: false })
        .limit(50)

      if (userOrders) {
        results.push(...userOrders.map((order: any) => ({
          ...order,
          searchType: 'user_orders',
          user_name: order.user_profiles?.name || 'Unknown User',
          user_email: order.user_profiles?.email || '<EMAIL>',
          product_name: order.products?.name || order.product_name || 'Unknown Product'
        })))
      }
    }

    if (searchType === 'all' || searchType === 'order') {
      // 📋 Search for specific orders by order details
      const { data: specificOrders } = await supabase
        .from('orders')
        .select(`
          *,
          user_profiles:user_id (
            id,
            name,
            email,
            game_id
          ),
          products:product_id (
            id,
            name,
            category,
            subcategory
          )
        `)
        .or(`
          id.ilike.%${query}%,
          game_id.ilike.%${query}%,
          amount.ilike.%${query}%,
          notes.ilike.%${query}%,
          products.name.ilike.%${query}%
        `.replace(/\s+/g, ''))
        .order('created_at', { ascending: false })
        .limit(50)

      if (specificOrders) {
        results.push(...specificOrders.map((order: any) => ({
          ...order,
          searchType: 'order_details',
          user_name: order.user_profiles?.name || 'Unknown User',
          user_email: order.user_profiles?.email || '<EMAIL>',
          product_name: order.products?.name || order.product_name || 'Unknown Product'
        })))
      }
    }

    // Remove duplicates based on order ID
    const uniqueResults = results.filter((order, index, self) => 
      index === self.findIndex(o => o.id === order.id)
    )

    console.log(`✅ Comprehensive search found ${uniqueResults.length} results`)

    return NextResponse.json({
      results: uniqueResults,
      totalCount: uniqueResults.length,
      searchQuery: query,
      searchType: searchType,
      message: uniqueResults.length > 0 
        ? `تم العثور على ${uniqueResults.length} نتيجة` 
        : 'لم يتم العثور على نتائج مطابقة'
    }, {
      headers: {
        'Cache-Control': 'private, max-age=10', // Cache for 10 seconds
        'Content-Type': 'application/json',
      },
    })
  } catch (error: any) {
    console.error('❌ Comprehensive order search error:', error)
    
    return NextResponse.json({
      results: [],
      totalCount: 0,
      searchQuery: '',
      searchType: 'error',
      message: 'حدث خطأ أثناء البحث'
    })
  }
} 