'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useSupabaseAuth } from '../../contexts/SupabaseAuthContext'
import { Product, User } from '../../types'
import { SSRProduct, SSRSystemSettings } from '../../lib/ssr-helpers'
import { getEffectivePrice, getEffectivePriceByRole } from '../../lib/utils/offers'
import PlayerIdInput from './PlayerIdInput'
import EnhancedProductCards from './EnhancedProductCards'
import PurchaseButton from './PurchaseButton'

import { useNotifications } from '../../hooks/use-notifications'
import { formatCurrencyForNotification } from '../../lib/utils/currency'

// Union type to handle both Product and SSRProduct
type ProductType = Product | SSRProduct

interface HomePageClientProps {
  initialProducts: SSRProduct[]
  initialSettings: SSRSystemSettings
}

export default function HomePageClient({ initialProducts, initialSettings }: HomePageClientProps) {
  const { user, updateProfile, refreshUserData } = useSupabaseAuth()
  const notifications = useNotifications()
  const [products, setProducts] = useState(initialProducts)
  const [selectedProduct, setSelectedProduct] = useState<ProductType | null>(null)
  const [playerId, setPlayerId] = useState('')
  const [loading, setLoading] = useState(false)
  const [isHydrated, setIsHydrated] = useState(false)
  const [productsLoading, setProductsLoading] = useState(false)

  // SIMPLE SOLUTION: Clear any old cached product data on mount
  useEffect(() => {
    try {
      // Clear any old product-related cache data
      const keysToRemove = []
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && (key.includes('product') || key.includes('cache') || key.includes('api'))) {
          keysToRemove.push(key)
        }
      }
      keysToRemove.forEach(key => localStorage.removeItem(key))
      
      if (keysToRemove.length > 0) {
        console.log('🧹 Cleared cached product data:', keysToRemove)
      }
    } catch (error) {
      console.log('⚠️ Could not clear cache:', error)
    }
  }, [])

  // Handle hydration to prevent SSR mismatch
  useEffect(() => {
    setIsHydrated(true)
  }, [])

  // SIMPLE SOLUTION: Always fetch fresh products on mount/refresh
  useEffect(() => {
    if (!isHydrated) return

    const fetchFreshProducts = async () => {
      setProductsLoading(true)
      try {
        // Clear any potential cached data and fetch fresh products
        const timestamp = new Date().getTime()
        const response = await fetch(`/api/products?fresh=${timestamp}`, {
          method: 'GET',
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        })
        
        if (response.ok) {
          const freshProducts = await response.json()
          
          if (freshProducts && Array.isArray(freshProducts)) {
            console.log('🔄 Loaded fresh products:', freshProducts.length)
            setProducts(freshProducts)
          }
        } else {
          console.log('⚠️ Failed to fetch fresh products, using SSR data')
        }
      } catch (error) {
        console.log('⚠️ Error fetching fresh products, using SSR data:', error)
      } finally {
        setProductsLoading(false)
      }
    }

    // Always fetch fresh data on component mount
    fetchFreshProducts()
  }, [isHydrated])

  const handlePurchase = async () => {
    if (!selectedProduct || !playerId) {
      notifications.warning(
        'بيانات ناقصة',
        'يرجى اختيار المنتج وإدخال ايدي اللاعب'
      )
      return
    }

    if (!user) {
      notifications.info(
        'تسجيل الدخول مطلوب',
        'يرجى تسجيل الدخول أولاً لإتمام عملية الشراء',
        [{
          label: 'تسجيل الدخول',
          onClick: () => window.location.href = '/auth',
          variant: 'primary'
        }]
      )
      return
    }

    // Use effective price (considering role and offers)
    const effectivePrice = getEffectivePriceByRole(selectedProduct, user.role)

    if ((typeof user.balance === 'number' ? user.balance : 0) < effectivePrice) {
      const currentBalance = formatCurrencyForNotification(typeof user.balance === 'number' ? user.balance : 0)
      const requiredAmount = formatCurrencyForNotification(effectivePrice)

      notifications.error(
        'رصيد غير كافي',
        `رصيدك الحالي ${currentBalance} غير كافي لشراء هذا المنتج (${requiredAmount})`,
        [{
          label: 'شحن الرصيد',
          onClick: () => window.location.href = '/profile',
          variant: 'primary'
        }]
      )
      return
    }

    setLoading(true)

    // Show loading notification
    const loadingId = notifications.showLoading('جاري معالجة طلبك...')

    try {
      // Create order via API
      const orderData = {
        userId: user.id,
        gameId: playerId,
        productName: selectedProduct.name,
        productId: selectedProduct.id,
        amount: selectedProduct.name,
        price: effectivePrice,
        notes: `Player ID: ${playerId}`
      }

      console.log('📝 Creating order:', orderData)

      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create order')
      }

      const result = await response.json()
      console.log('✅ Order created successfully:', result)

      // Store the old balance before refresh
      const oldBalance = typeof user.balance === 'number' ? user.balance : 0
      console.log('💰 Purchase flow - Initial balance:', oldBalance)

      // Refresh user data to get updated balance from database
      // The transaction system has already updated the balance in the database
      let updatedUser: User | null = null
      try {
        console.log('🔄 Refreshing user data after order creation...')

        // Add timeout to prevent hanging
        const refreshPromise = refreshUserData()
        const timeoutPromise = new Promise<User | null>((_, reject) =>
          setTimeout(() => reject(new Error('Refresh timeout')), 5000)
        )

        updatedUser = await Promise.race([refreshPromise, timeoutPromise])
        console.log('✅ User data refreshed successfully', {
          oldBalance,
          newBalance: updatedUser?.balance
        })

        // Small delay to ensure state has been updated
        await new Promise(resolve => setTimeout(resolve, 100))
      } catch (refreshError) {
        // Continue with success flow even if refresh fails
        if (process.env.NODE_ENV === 'development') {
          console.warn('⚠️ Failed to refresh user data, but order was created:', refreshError)
        }
      }

      // Dismiss loading and show success
      notifications.dismiss(loadingId)
      const deductedAmount = formatCurrencyForNotification(effectivePrice)

      notifications.success(
        '🎉 تم إرسال طلبك بنجاح!',
        `رقم الطلب: ${result.order.id}\nتم خصم ${deductedAmount} من رصيدك. سيتم معالجة طلبك قريباً`,
        [{
          label: 'عرض طلباتي',
          onClick: () => window.location.href = '/wallet',
          variant: 'primary'
        }]
      )

      // Show balance update notification with correct balance
      // Use the actual updated balance if refresh succeeded, otherwise calculate it
      const newBalance = updatedUser?.balance ?? (oldBalance - effectivePrice)

      notifications.balanceUpdate(effectivePrice, 'debit', newBalance)

      // ✅ SIMPLE: No cross-tab updates - user can refresh wallet page manually

      setSelectedProduct(null)
      setPlayerId('')
    } catch (error: any) {
      // Dismiss loading and show error
      notifications.dismiss(loadingId)

      // Handle specific error types
      if (error.message?.includes('permission') || error.message?.includes('insufficient')) {
        notifications.error(
          'مشكلة في الصلاحيات',
          'حدثت مشكلة في الصلاحيات. يرجى تسجيل الخروج والدخول مرة أخرى',
          [{
            label: 'تسجيل الخروج',
            onClick: () => window.location.href = '/auth',
            variant: 'primary'
          }, {
            label: 'إعادة المحاولة',
            onClick: () => handlePurchase(),
            variant: 'secondary'
          }]
        )
      } else {
        notifications.error(
          'فشل في إرسال الطلب',
          error.message || 'حدث خطأ غير متوقع أثناء إرسال الطلب',
          [{
            label: 'إعادة المحاولة',
            onClick: () => handlePurchase(),
            variant: 'primary'
          }]
        )
      }
    } finally {
      setLoading(false)
      // Safety net: ensure loading notification is dismissed
      try {
        notifications.dismiss(loadingId)
        console.log('🛡️ Safety net: Loading notification dismissed in finally block')
      } catch (dismissError) {
        console.warn('⚠️ Failed to dismiss loading notification in finally block:', dismissError)
      }
    }
  }

  // Note: Home page should be accessible to all users regardless of status
  // Account status protection is handled by AccountStatusGuard on protected pages only

  return (
    <>
      {/* User Balance Display - only show after hydration to prevent SSR mismatch */}
      {isHydrated && user && (
        <div className="text-center mb-6">
          <div className="inline-flex items-center gap-2 bg-gray-800 px-4 py-2 rounded-lg">
            <span className="text-gray-400">رصيدك الحالي:</span>
            <span className="text-green-400 font-bold">
              {formatCurrencyForNotification(typeof user.balance === 'number' ? user.balance : 0)}
            </span>
            <span className="text-xs text-green-400">📊 مباشر</span>
          </div>
        </div>
      )}

      {/* Player ID Input Section */}
      <div className="mb-8">
        <PlayerIdInput playerId={playerId} setPlayerId={setPlayerId} />
      </div>

      {/* Products Section */}
      <div className="mb-8">
        <h2 className="text-2xl md:text-3xl font-bold text-white mb-6 text-center">إختر المنتج المناسب لك:</h2>
        {products.length === 0 ? (
          <div className="text-center text-gray-400 py-8">
            <div className="text-lg mb-2">لا توجد منتجات متاحة حالياً</div>
            <div className="text-sm">يرجى المحاولة لاحقاً أو التواصل مع الدعم الفني</div>
          </div>
        ) : (
          <EnhancedProductCards
            products={products.filter(p => p.isActive)}
            selectedProduct={selectedProduct}
            setSelectedProduct={setSelectedProduct}
            loading={productsLoading}
            user={user}
          />
        )}
      </div>

      {/* Purchase Button */}
      <div className="text-center">
        <PurchaseButton
          onPurchase={handlePurchase}
          disabled={!selectedProduct || !playerId || loading}
          loading={loading}
        />
      </div>

      {/* Login Prompt for Non-Authenticated Users - only show after hydration */}
      {isHydrated && !user && (
        <div className="mt-8 text-center">
          <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
            <p className="text-yellow-400 mb-2">
              يرجى تسجيل الدخول أو إنشاء حساب جديد لإتمام عملية الشراء
            </p>
            <Link
              href="/auth"
              className="inline-block bg-red-800 hover:bg-red-900 text-white px-6 py-2 rounded-lg transition-colors"
            >
              تسجيل الدخول / إنشاء حساب
            </Link>
          </div>
        </div>
      )}

    </>
  )
}
