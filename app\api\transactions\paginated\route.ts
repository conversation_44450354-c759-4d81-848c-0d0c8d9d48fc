/**
 * Paginated Transactions API Route
 * ✅ PERFORMANCE: Server-side pagination for large transaction datasets
 */

import { NextRequest, NextResponse } from 'next/server'
import { TransactionService } from '../../../../lib/services/transactions'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const page = parseInt(searchParams.get('page') || '1')
    const pageSize = parseInt(searchParams.get('pageSize') || '10')

    // Validate parameters
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    if (page < 1 || pageSize < 1 || pageSize > 50) {
      return NextResponse.json(
        { error: 'Invalid page or pageSize parameters' },
        { status: 400 }
      )
    }

    console.log(`📊 Fetching paginated transactions for user: ${userId}, page: ${page}, pageSize: ${pageSize}`)

    // ✅ PERFORMANCE: Get paginated results with total count
    const result = await TransactionService.getUserTransactionsPaginated(userId, page, pageSize)

    console.log(`✅ Fetched ${result.transactions.length} transactions (page ${page}/${result.totalPages})`)

    return NextResponse.json({
      transactions: result.transactions,
      totalCount: result.totalCount,
      totalPages: result.totalPages,
      currentPage: page,
      pageSize: pageSize,
      hasNextPage: page < result.totalPages,
      hasPreviousPage: page > 1
    }, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate', // No caching for real-time data
        'Pragma': 'no-cache',
        'Expires': '0',
        'Content-Type': 'application/json',
      },
    })
  } catch (error: any) {
    console.error('❌ Paginated transactions API error:', error)
    
    // Return empty result instead of error for graceful handling
    return NextResponse.json({
      transactions: [],
      totalCount: 0,
      totalPages: 0,
      currentPage: 1,
      pageSize: 10,
      hasNextPage: false,
      hasPreviousPage: false
    })
  }
} 