'use client'

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useState } from 'react'

interface QueryProviderProps {
  children: React.ReactNode
}

export default function QueryProvider({ children }: QueryProviderProps) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // ✅ IMPROVED: Better balance for real-time updates
            staleTime: 2 * 60 * 1000, // 2 minutes (reduced from 5)
            gcTime: 5 * 60 * 1000, // 5 minutes (reduced from 10)
            // ✅ IMPROVED: Enable refetch on focus for better UX
            refetchOnWindowFocus: true,
            // Retry failed requests 2 times
            retry: 2,
            // Retry delay function
            retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
          },
          mutations: {
            // Retry failed mutations once
            retry: 1,
          },
        },
      })
  )

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {/* DevTools disabled for cleaner UI */}
    </QueryClientProvider>
  )
}
