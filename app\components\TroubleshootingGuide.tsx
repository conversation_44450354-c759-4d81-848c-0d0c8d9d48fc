'use client'

import { useState, useEffect } from 'react'
import { AlertTriangle, Shield, Wifi, RefreshCw, CheckCircle, X } from 'lucide-react'

interface TroubleshootingGuideProps {
  error?: string
  onClose?: () => void
}

export default function TroubleshootingGuide({ error, onClose }: TroubleshootingGuideProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [whatsAppNumber, setWhatsAppNumber] = useState<string>("+************")

  // Fetch WhatsApp number from admin settings
  useEffect(() => {
    const fetchWhatsAppNumber = async () => {
      try {
        const { AdminService } = await import('../../lib/services/admin')
        const settings = await AdminService.getSystemSettings()
        if (settings && settings.supportWhatsApp) {
          setWhatsAppNumber(settings.supportWhatsApp)
        }
      } catch (error) {
        console.warn('Could not fetch WhatsApp number from settings, using default:', error)
      }
    }

    fetchWhatsAppNumber()
  }, [])

  const troubleshootingSteps = [
    {
      title: 'تعطيل مانع الإعلانات',
      description: 'قم بتعطيل مانع الإعلانات (AdBlock) مؤقتاً',
      icon: Shield,
      steps: [
        'انقر على أيقونة مانع الإعلانات في المتصفح',
        'اختر "تعطيل في هذا الموقع" أو "Disable on this site"',
        'أعد تحميل الصفحة'
      ]
    },
    {
      title: 'إضافة الموقع للقائمة البيضاء',
      description: 'أضف الموقع للقائمة البيضاء في مانع الإعلانات',
      icon: CheckCircle,
      steps: [
        'افتح إعدادات مانع الإعلانات',
        'ابحث عن "القائمة البيضاء" أو "Whitelist"',
        'أضف localhost أو عنوان الموقع',
        'احفظ الإعدادات وأعد تحميل الصفحة'
      ]
    },
    {
      title: 'التحقق من الاتصال',
      description: 'تأكد من اتصالك بالإنترنت',
      icon: Wifi,
      steps: [
        'تحقق من اتصال الإنترنت',
        'جرب فتح موقع آخر للتأكد',
        'أعد تشغيل الراوتر إذا لزم الأمر'
      ]
    },
    {
      title: 'إعادة تحميل الصفحة',
      description: 'أعد تحميل الصفحة أو امسح الكاش',
      icon: RefreshCw,
      steps: [
        'اضغط Ctrl+F5 (أو Cmd+Shift+R على Mac)',
        'أو امسح كاش المتصفح',
        'أعد تحميل الصفحة'
      ]
    }
  ]

  const isConnectionError = error?.includes('ERR_BLOCKED_BY_CLIENT') || 
                           error?.includes('network') || 
                           error?.includes('Firebase')

  if (!isConnectionError && !error) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-gray-800 border border-gray-600 rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-600">
          <div className="flex items-center gap-3">
            <AlertTriangle className="w-6 h-6 text-yellow-500" />
            <h2 className="text-xl font-bold text-white">حل مشاكل الاتصال</h2>
          </div>
          {onClose && (
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          )}
        </div>

        {/* Error Message */}
        {error && (
          <div className="p-6 border-b border-gray-600">
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          </div>
        )}

        {/* Steps */}
        <div className="p-6">
          <p className="text-gray-300 mb-6">
            يبدو أن هناك مشكلة في الاتصال. جرب الحلول التالية بالترتيب:
          </p>

          <div className="space-y-4">
            {troubleshootingSteps.map((step, index) => {
              const StepIcon = step.icon
              const isActive = index === currentStep
              const isCompleted = index < currentStep

              return (
                <div
                  key={index}
                  className={`border rounded-lg p-4 transition-colors ${
                    isActive 
                      ? 'border-blue-500 bg-blue-500/10' 
                      : isCompleted 
                      ? 'border-green-500 bg-green-500/10' 
                      : 'border-gray-600 bg-gray-900'
                  }`}
                >
                  <div className="flex items-start gap-4">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                      isActive 
                        ? 'bg-blue-600' 
                        : isCompleted 
                        ? 'bg-green-600' 
                        : 'bg-gray-600'
                    }`}>
                      {isCompleted ? (
                        <CheckCircle className="w-4 h-4 text-white" />
                      ) : (
                        <StepIcon className="w-4 h-4 text-white" />
                      )}
                    </div>

                    <div className="flex-1">
                      <h3 className={`font-semibold mb-2 ${
                        isActive ? 'text-blue-400' : isCompleted ? 'text-green-400' : 'text-white'
                      }`}>
                        {index + 1}. {step.title}
                      </h3>
                      <p className="text-gray-400 text-sm mb-3">{step.description}</p>

                      {isActive && (
                        <ul className="space-y-2">
                          {step.steps.map((stepDetail, stepIndex) => (
                            <li key={stepIndex} className="flex items-start gap-2 text-sm text-gray-300">
                              <span className="text-blue-400 mt-1">•</span>
                              <span>{stepDetail}</span>
                            </li>
                          ))}
                        </ul>
                      )}
                    </div>
                  </div>

                  {isActive && (
                    <div className="flex gap-3 mt-4">
                      <button
                        onClick={() => setCurrentStep(Math.min(currentStep + 1, troubleshootingSteps.length - 1))}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition-colors"
                      >
                        جربت هذا الحل
                      </button>
                      {index > 0 && (
                        <button
                          onClick={() => setCurrentStep(currentStep - 1)}
                          className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm transition-colors"
                        >
                          الخطوة السابقة
                        </button>
                      )}
                    </div>
                  )}
                </div>
              )
            })}
          </div>

          {/* Final Actions */}
          <div className="mt-6 pt-6 border-t border-gray-600">
            <div className="bg-gray-900 border border-gray-600 rounded-lg p-4">
              <h4 className="text-white font-semibold mb-2">لا تزال المشكلة موجودة؟</h4>
              <p className="text-gray-400 text-sm mb-3">
                إذا لم تنجح الحلول السابقة، يمكنك:
              </p>
              <div className="flex flex-col sm:flex-row gap-3">
                <button
                  onClick={() => window.location.reload()}
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm transition-colors flex items-center justify-center gap-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  إعادة تحميل الصفحة
                </button>
                <button
                  onClick={() => {
                    const message = `مرحباً، أواجه مشكلة في الاتصال بالموقع:\n\nالخطأ: ${error || 'مشكلة في الاتصال'}\n\nيرجى المساعدة.`
                    const phoneNumber = whatsAppNumber.replace(/\+/g, '')
                    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`
                    window.open(whatsappUrl, '_blank')
                  }}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition-colors"
                >
                  التواصل مع الدعم
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
