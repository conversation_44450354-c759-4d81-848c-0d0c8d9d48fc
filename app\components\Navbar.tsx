"use client"

import { useState } from "react"
import { <PERSON>u, X, Wallet, LogOut, Crown } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useSupabaseAuth } from "../../contexts/SupabaseAuthContext"
import CopyButton from "./CopyButton"
import ClientOnly from "../../components/ClientOnly"

export default function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const { user, signOut } = useSupabaseAuth()

  const handleSignOut = async () => {
    try {
      await signOut()
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error signing out:', error)
      }
    }
  }

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 border-b border-gray-700 backdrop-blur-sm" style={{ backgroundColor: "rgba(26, 26, 26, 0.95)" }}>
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center cursor-pointer">
            <div
              className="w-10 h-10 rounded-lg flex items-center justify-center overflow-hidden"
              style={{ backgroundColor: "#8B2635" }}
            >
              <Image
                src="/images/neran-logo.png"
                alt="نيران كارد - Neran Card"
                width={40}
                height={40}
                className="rounded object-contain"
              />
            </div>
            <div className="mr-3 hidden md:block">
              <div className="text-white font-bold text-lg">نيران كارد</div>
              <div className="text-gray-400 text-xs">Neran Card</div>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-reverse space-x-8">
            <a href="/" className="text-white hover:text-red-400 transition-colors">
              الرئيسية
            </a>
            <ClientOnly>
              {user && (
                <>
                  <a href="/profile" className="text-white hover:text-red-400 transition-colors">
                    الملف الشخصي
                  </a>
                  <a href="/wallet" className="text-white hover:text-red-400 transition-colors">
                    المحفظة
                  </a>
                  {/* Show admin link only to confirmed admin users */}
                  {user.role === 'admin' && user.status === 'active' && (
                    <a href="/admin" className="text-white hover:text-red-400 transition-colors">
                      لوحة التحكم
                    </a>
                  )}
                </>
              )}
            </ClientOnly>
          </div>

          {/* User Info & Actions */}
          <div className="hidden md:flex items-center gap-4">
            <ClientOnly fallback={
              <div className="bg-gray-800 px-4 py-2 rounded-lg">
                <div className="text-gray-400 text-sm">جاري التحميل...</div>
              </div>
            }>
              {user ? (
                <>
                  <div className="flex items-center gap-2 bg-gray-800 px-3 py-2 rounded-lg">
                    <Wallet className="w-4 h-4 text-red-400" />
                    <span className="text-white font-semibold">${(typeof user.balance === 'number' ? user.balance : 0).toFixed(2)}</span>
                  </div>
                  <div className="text-white text-sm">
                    <div className="font-medium flex items-center gap-2">
                      {user.name}
                      {user.role === 'admin' && user.status === 'active' && (
                        <span className="bg-purple-600 text-white px-2 py-0.5 rounded text-xs flex items-center gap-1">
                          <Crown className="w-3 h-3" />
                          مدير
                        </span>
                      )}
                    </div>
                    <div className="text-gray-400 text-xs flex items-center gap-1">
                      <span>ID: {user.id}</span>
                      <CopyButton text={user.id} size="sm" />
                    </div>
                  </div>
                  <button
                    onClick={handleSignOut}
                    className="text-gray-400 hover:text-red-400 transition-colors p-2"
                    title="تسجيل الخروج"
                  >
                    <LogOut className="w-5 h-5" />
                  </button>
                </>
              ) : (
                <Link
                  href="/auth"
                  className="bg-red-800 hover:bg-red-900 text-white px-4 py-2 rounded-lg transition-colors text-sm"
                >
                  تسجيل الدخول
                </Link>
              )}
            </ClientOnly>
          </div>

          {/* Mobile Balance & Menu */}
          <div className="md:hidden flex items-center gap-3">
            <ClientOnly fallback={
              <div className="bg-gray-800 px-2 py-1 rounded text-xs text-gray-400">
                جاري التحميل...
              </div>
            }>
              {user && (
                <div className="flex items-center gap-1 bg-gray-800 px-2 py-1 rounded-lg">
                  <Wallet className="w-3 h-3 text-red-400" />
                  <span className="text-white font-semibold text-xs">${(typeof user.balance === 'number' ? user.balance : 0).toFixed(2)}</span>
                </div>
              )}
            </ClientOnly>

            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2 rounded-lg hover:bg-gray-800 transition-colors"
              aria-label="فتح القائمة"
            >
              {isMenuOpen ? <X className="w-6 h-6 text-white" /> : <Menu className="w-6 h-6 text-white" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-gray-700 py-4">
            <ClientOnly fallback={
              <div className="px-4 py-2 text-center text-gray-400">
                جاري التحميل...
              </div>
            }>
              {user ? (
                <>
                  <div className="px-4 py-2 mb-4 bg-gray-800 rounded-lg mx-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-white font-medium flex items-center gap-2">
                          {user.name}
                          {user.role === 'admin' && user.status === 'active' && (
                            <span className="bg-purple-600 text-white px-1.5 py-0.5 rounded text-xs flex items-center gap-1">
                              <Crown className="w-2.5 h-2.5" />
                              مدير
                            </span>
                          )}
                        </div>
                        <div className="text-gray-400 text-xs flex items-center gap-1">
                          <span>ID: {user.id}</span>
                          <CopyButton text={user.id} size="sm" />
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Wallet className="w-4 h-4 text-red-400" />
                        <span className="text-white font-semibold">${(typeof user.balance === 'number' ? user.balance : 0).toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <a href="/" className="block px-4 py-2 text-white hover:bg-gray-800 rounded-lg transition-colors">
                      الرئيسية
                    </a>
                    <a href="/profile" className="block px-4 py-2 text-white hover:bg-gray-800 rounded-lg transition-colors">
                      الملف الشخصي
                    </a>
                    <a href="/wallet" className="block px-4 py-2 text-white hover:bg-gray-800 rounded-lg transition-colors">
                      المحفظة
                    </a>
                    {/* Show admin link only to confirmed admin users */}
                    {user.role === 'admin' && user.status === 'active' && (
                      <a href="/admin" className="block px-4 py-2 text-white hover:bg-gray-800 rounded-lg transition-colors">
                        لوحة التحكم
                      </a>
                    )}
                    <button
                      onClick={handleSignOut}
                      className="block w-full text-left px-4 py-2 text-red-400 hover:bg-gray-800 rounded-lg transition-colors"
                    >
                      تسجيل الخروج
                    </button>
                  </div>
                </>
              ) : (
                <div className="px-4">
                  <Link
                    href="/auth"
                    className="block w-full bg-red-800 hover:bg-red-900 text-white py-2 rounded-lg transition-colors text-center"
                  >
                    تسجيل الدخول
                  </Link>
                </div>
              )}
            </ClientOnly>
          </div>
        )}


      </div>
    </nav>
  )
}
