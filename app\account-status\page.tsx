"use client"

import { useState, useEffect } from "react"
import { useSupabaseAuth } from "../../contexts/SupabaseAuthContext"
import { Clock, CheckCircle, XCircle, AlertCircle, User, Mail, Calendar, MessageCircle } from "lucide-react"
import { formatDateTime } from "../../lib/utils/date-utils"

function AccountStatusPage() {
  const { user } = useSupabaseAuth()
  const [loading, setLoading] = useState(true)
  const [whatsAppNumber, setWhatsAppNumber] = useState<string>("+************")

  useEffect(() => {
    if (user) {
      setLoading(false)
    }
  }, [user])

  // Fetch WhatsApp number from admin settings
  useEffect(() => {
    const fetchWhatsAppNumber = async () => {
      try {
        const response = await fetch('/api/settings')
        if (response.ok) {
          const settings = await response.json()
          if (settings && settings.supportWhatsApp) {
            setWhatsAppNumber(settings.supportWhatsApp)
          }
        }
      } catch (error) {
        console.warn('Could not fetch WhatsApp number from settings, using default:', error)
      }
    }

    fetchWhatsAppNumber()
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-red-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <div className="text-white text-lg">جاري التحميل...</div>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center">
        <div className="text-center text-white">
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold mb-2">غير مسجل دخول</h1>
          <p className="text-gray-400">يرجى تسجيل الدخول لعرض حالة الحساب</p>
        </div>
      </div>
    )
  }

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'pending':
        return {
          icon: <Clock className="w-8 h-8 text-yellow-500" />,
          title: 'في انتظار الموافقة',
          description: 'حسابك قيد المراجعة من قبل الإدارة',
          color: 'yellow',
          bgColor: 'bg-yellow-500/20',
          borderColor: 'border-yellow-500/30',
          textColor: 'text-yellow-400'
        }
      case 'active':
        return {
          icon: <CheckCircle className="w-8 h-8 text-green-500" />,
          title: 'حساب نشط',
          description: 'حسابك مفعل ويمكنك استخدام جميع الخدمات',
          color: 'green',
          bgColor: 'bg-green-500/20',
          borderColor: 'border-green-500/30',
          textColor: 'text-green-400'
        }
      case 'suspended':
        return {
          icon: <XCircle className="w-8 h-8 text-red-500" />,
          title: 'حساب معلق',
          description: 'تم تعليق حسابك، يرجى التواصل مع الإدارة',
          color: 'red',
          bgColor: 'bg-red-500/20',
          borderColor: 'border-red-500/30',
          textColor: 'text-red-400'
        }
      default:
        return {
          icon: <AlertCircle className="w-8 h-8 text-gray-500" />,
          title: 'حالة غير معروفة',
          description: 'حالة الحساب غير محددة',
          color: 'gray',
          bgColor: 'bg-gray-500/20',
          borderColor: 'border-gray-500/30',
          textColor: 'text-gray-400'
        }
    }
  }

  const statusInfo = getStatusInfo(user.status)

  const formatDate = (dateString: string) => {
    try {
      return formatDateTime(dateString)
    } catch {
      return 'غير محدد'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-red-500 to-red-600 rounded-2xl mb-4 shadow-lg">
            <User className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-2xl md:text-3xl font-bold text-white mb-2">حالة الحساب</h1>
          <p className="text-gray-400 text-sm md:text-base">معلومات حسابك وحالة التفعيل</p>
        </div>

        {/* Status Card */}
        <div className={`${statusInfo.bgColor} ${statusInfo.borderColor} border rounded-3xl p-8 mb-8 shadow-2xl`}>
          <div className="text-center mb-6">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-white/10 rounded-3xl mb-4 backdrop-blur-sm">
              {statusInfo.icon}
            </div>
            <h2 className={`text-2xl font-bold ${statusInfo.textColor} mb-2`}>{statusInfo.title}</h2>
            <p className="text-gray-300 text-lg">{statusInfo.description}</p>
          </div>

          {/* Status-specific content */}
          {user.status === 'pending' && (
            <div className="bg-white/5 rounded-2xl p-6 backdrop-blur-sm">
              <h3 className="text-white font-semibold mb-4 text-center">ماذا يحدث الآن؟</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3 text-gray-300">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                  <span>تم إنشاء حسابك بنجاح</span>
                </div>
                <div className="flex items-center gap-3 text-gray-300">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
                  <span>جاري مراجعة بياناتك من قبل الإدارة</span>
                </div>
                <div className="flex items-center gap-3 text-gray-400">
                  <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
                  <span>سيتم إشعارك عند تفعيل الحساب</span>
                </div>
              </div>

              <div className="mt-6 p-4 bg-blue-500/20 rounded-xl border border-blue-500/30">
                <p className="text-blue-300 text-sm text-center">
                  <strong>ملاحظة:</strong> عادة ما تستغرق عملية المراجعة من 24-48 ساعة
                </p>
              </div>

              <div className="mt-6 p-4 bg-orange-500/20 rounded-xl border border-orange-500/30">
                <p className="text-orange-300 text-sm text-center">
                  <strong>تنبيه:</strong> لا يمكنك الوصول للصفحات المحمية (المحفظة، الملف الشخصي) حتى يتم تفعيل حسابك
                </p>
              </div>

              {/* WhatsApp Contact */}
              <div className="mt-6">
                <button
                  onClick={() => {
                    const message = `🔥 *طلب تفعيل حساب - نيران كارد*\n\n` +
                      `👤 *معرف المستخدم:* ${user.id}\n` +
                      `📧 *الاسم:* ${user.name}\n` +
                      `📧 *البريد الإلكتروني:* ${user.email}\n\n` +
                      `مرحباً، أريد تفعيل حسابي الجديد بأسرع وقت ممكن.\n\n` +
                      `شكراً لكم 🙏`

                    const phoneNumber = whatsAppNumber.replace(/\+/g, '')
                    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`
                    window.open(whatsappUrl, '_blank')
                  }}
                  className="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-6 rounded-xl transition-all duration-200 flex items-center justify-center gap-3 shadow-lg hover:shadow-xl transform hover:scale-[1.02]"
                >
                  <MessageCircle className="w-5 h-5" />
                  <span className="font-semibold">تسريع التفعيل عبر واتساب</span>
                </button>
              </div>
            </div>
          )}

          {user.status === 'active' && (
            <div className="bg-white/5 rounded-2xl p-6 backdrop-blur-sm">
              <h3 className="text-white font-semibold mb-4 text-center">يمكنك الآن:</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-3 text-green-300">
                  <CheckCircle className="w-5 h-5" />
                  <span>تصفح وشراء المنتجات</span>
                </div>
                <div className="flex items-center gap-3 text-green-300">
                  <CheckCircle className="w-5 h-5" />
                  <span>شحن رصيد المحفظة</span>
                </div>
                <div className="flex items-center gap-3 text-green-300">
                  <CheckCircle className="w-5 h-5" />
                  <span>تتبع الطلبات</span>
                </div>
                <div className="flex items-center gap-3 text-green-300">
                  <CheckCircle className="w-5 h-5" />
                  <span>الوصول لجميع الخدمات</span>
                </div>
              </div>
            </div>
          )}

          {user.status === 'suspended' && (
            <div className="bg-white/5 rounded-2xl p-6 backdrop-blur-sm">
              <h3 className="text-white font-semibold mb-4 text-center">للتواصل مع الإدارة:</h3>
              <div className="text-center">
                <button
                  onClick={() => {
                    const message = `🚨 *استفسار حول تعليق الحساب*\n\n` +
                      `👤 *معرف المستخدم:* ${user.id}\n` +
                      `📧 *البريد الإلكتروني:* ${user.email}\n\n` +
                      `مرحباً، حسابي معلق وأريد معرفة السبب وكيفية إعادة تفعيله.\n\n` +
                      `شكراً لكم 🙏`

                    const phoneNumber = whatsAppNumber.replace(/\+/g, '')
                    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`
                    window.open(whatsappUrl, '_blank')
                  }}
                  className="bg-green-600 hover:bg-green-700 text-white py-3 px-6 rounded-xl transition-all duration-200 flex items-center justify-center gap-3 mx-auto"
                >
                  <Mail className="w-5 h-5" />
                  <span>تواصل عبر واتساب</span>
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Account Details */}
        <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 shadow-xl">
          <h3 className="text-xl font-bold text-white mb-6 flex items-center gap-3">
            <User className="w-6 h-6" />
            تفاصيل الحساب
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <label className="text-gray-400 text-sm">الاسم</label>
                <div className="text-white font-medium">{user.name}</div>
              </div>
              <div>
                <label className="text-gray-400 text-sm">البريد الإلكتروني</label>
                <div className="text-white font-medium">{user.email}</div>
              </div>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="text-gray-400 text-sm">نوع الحساب</label>
                <div className="text-white font-medium">
                  {user.role === 'admin' ? 'مدير' : 'مستخدم'}
                </div>
              </div>
              <div>
                <label className="text-gray-400 text-sm">تاريخ التسجيل</label>
                <div className="text-white font-medium flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  {formatDate(user.createdAt)}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="mt-8 text-center">
          <button
            onClick={() => window.history.back()}
            className="bg-gray-600 hover:bg-gray-700 text-white py-3 px-6 rounded-xl transition-all duration-200"
          >
            العودة
          </button>
        </div>
      </div>
    </div>
  )
}

export default AccountStatusPage
