import { NextRequest, NextResponse } from 'next/server'
import { SupabaseAdminService } from '../../../../lib/services/supabase-admin'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    console.log(`🔄 User Status API: Checking status for user ${userId}`)

    // Get current user status from database
    const user = await SupabaseAdminService.getUserById(userId)
    
    if (!user) {
      console.log(`❌ User Status API: User ${userId} not found`)
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    console.log(`✅ User Status API: User ${userId} status is ${user.status}`)
    
    return NextResponse.json({
      id: user.id,
      name: user.name,
      email: user.email,
      status: user.status,
      role: user.role,
      balance: user.balance,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    })
  } catch (error: any) {
    console.error('❌ User Status API: Error checking user status:', error)
    return NextResponse.json(
      { error: 'Failed to check user status' },
      { status: 500 }
    )
  }
}
