/**
 * ✅ COMPREHENSIVE USER SEARCH API
 * Enhanced search that can find users by any field including related data
 */

import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '../../../../../lib/supabase-server'

export async function GET(request: NextRequest) {
  try {
    // 🔒 ADMIN AUTHENTICATION
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check admin privileges
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('role, status')
      .eq('id', user.id)
      .single()

    if (profileError || !profile || profile.role !== 'admin' || profile.status !== 'active') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q') || ''
    const searchType = searchParams.get('type') || 'all' // 'all', 'profile', 'activity'

    if (!query.trim()) {
      return NextResponse.json({
        results: [],
        searchType: 'empty',
        message: 'Please enter a search term'
      })
    }

    console.log(`🔍 Admin comprehensive user search - query: "${query}", type: ${searchType}`)

    let results = []

    if (searchType === 'all' || searchType === 'profile') {
      // 📋 Search for users by profile data
      const { data: profileUsers } = await supabase
        .from('user_profiles')
        .select(`
          *
        `)
        .or(`
          id.eq.${query},
          name.ilike.%${query}%,
          email.ilike.%${query}%,
          role.ilike.%${query}%,
          status.ilike.%${query}%
        `.replace(/\s+/g, ''))
        .order('created_at', { ascending: false })
        .limit(50)

      if (profileUsers) {
        results.push(...profileUsers.map((user: any) => ({
          ...user,
          searchType: 'user_profile',
          matchField: getMatchField(user, query)
        })))
      }
    }

    if (searchType === 'all' || searchType === 'activity') {
      // 📋 Search for users by their order activity
      const { data: orderUsers } = await supabase
        .from('orders')
        .select(`
          user_id,
          user_profiles:user_id (
            *
          )
        `)
        .or(`
          game_id.ilike.%${query}%,
          amount.ilike.%${query}%,
          notes.ilike.%${query}%
        `.replace(/\s+/g, ''))
        .order('created_at', { ascending: false })
        .limit(30)

      if (orderUsers) {
        // Get unique users from order activity
        const uniqueUsers = new Map()
        orderUsers.forEach((order: any) => {
          if (order.user_profiles && !uniqueUsers.has(order.user_id)) {
            uniqueUsers.set(order.user_id, {
              ...order.user_profiles,
              searchType: 'user_activity',
              matchField: 'order_activity'
            })
          }
        })
        results.push(...Array.from(uniqueUsers.values()))
      }
    }

    // Remove duplicates based on user ID
    const uniqueResults = results.filter((user, index, self) => 
      index === self.findIndex(u => u.id === user.id)
    )

    console.log(`✅ Comprehensive user search found ${uniqueResults.length} results`)

    return NextResponse.json({
      results: uniqueResults,
      totalCount: uniqueResults.length,
      searchQuery: query,
      searchType: searchType,
      message: uniqueResults.length > 0 
        ? `تم العثور على ${uniqueResults.length} مستخدم` 
        : 'لم يتم العثور على مستخدمين مطابقين'
    }, {
      headers: {
        'Cache-Control': 'private, max-age=10', // Cache for 10 seconds
        'Content-Type': 'application/json',
      },
    })
  } catch (error: any) {
    console.error('❌ Comprehensive user search error:', error)
    
    return NextResponse.json({
      results: [],
      totalCount: 0,
      searchQuery: '',
      searchType: 'error',
      message: 'حدث خطأ أثناء البحث'
    })
  }
}

// Helper function to determine which field matched
function getMatchField(user: any, query: string): string {
  const q = query.toLowerCase()
  if (user.id === query) return 'id'
  if (user.name?.toLowerCase().includes(q)) return 'name'
  if (user.email?.toLowerCase().includes(q)) return 'email'
  if (user.role?.toLowerCase().includes(q)) return 'role'
  if (user.status?.toLowerCase().includes(q)) return 'status'
  return 'unknown'
} 