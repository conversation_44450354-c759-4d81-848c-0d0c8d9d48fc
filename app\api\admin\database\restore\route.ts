import { NextRequest, NextResponse } from 'next/server'
import { DatabaseManager } from '../../../../../lib/services/database-manager'

// POST - Restore from backup
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { backupId, overwrite = false, tables } = body

    if (!backupId) {
      return NextResponse.json(
        { error: 'Backup ID is required' },
        { status: 400 }
      )
    }

    console.log(`🔧 Admin API: Restoring backup: ${backupId}`)

    await DatabaseManager.restoreBackup(backupId, { overwrite, tables })

    console.log('✅ Admin API: Backup restored successfully')

    return NextResponse.json({ success: true })
  } catch (error: any) {
    console.error('❌ Admin API: Error restoring backup:', error)
    return NextResponse.json(
      { error: 'Failed to restore backup' },
      { status: 500 }
    )
  }
}
