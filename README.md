# Neran Card - Gaming Card Application

A modern gaming card application built with Next.js, Supabase, and Tailwind CSS.

## Latest Update - Migrated to Supabase
- ✅ Migrated from Firebase to Supabase for better performance
- ✅ PostgreSQL relational database for complex queries
- ✅ Improved admin dashboard with efficient SQL operations
- ✅ Row Level Security for enhanced data protection
- ✅ Server-side pagination for better scalability

## Features
- 🏷️ Advanced offers system with "عرض" badges
- 📱 Mobile-first responsive design
- 🔐 Supabase authentication and PostgreSQL database
- 🛠️ Admin product management with relational queries
- 💰 Wallet and balance system
- ⚡ Real-time updates with Supabase subscriptions

## Deployment Status
Latest commit: e567475 - All Firebase Auth issues resolved
