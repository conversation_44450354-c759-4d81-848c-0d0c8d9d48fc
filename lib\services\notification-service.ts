/**
 * Universal Notification Service
 * Can show notifications anywhere on the website
 */

import { formatCurrencyForNotification, DEFAULT_CURRENCY } from '../utils/currency'

export interface NotificationAction {
  label: string
  onClick: () => void
  variant?: 'primary' | 'secondary' | 'danger'
  loading?: boolean
}

export interface NotificationData {
  id: string
  type: 'success' | 'error' | 'warning' | 'info' | 'promotion' | 'custom'
  title: string
  message: string
  icon?: string
  duration?: number // Auto-dismiss after X milliseconds (0 = no auto-dismiss)
  position?: 'top-right' | 'top-left' | 'top-center' | 'bottom-right' | 'bottom-left' | 'bottom-center' | 'center'
  actions?: NotificationAction[]
  dismissible?: boolean
  persistent?: boolean // Survives page refresh
  priority?: 'low' | 'normal' | 'high' | 'urgent'
  gradient?: string
  customStyles?: string
}

type NotificationListener = (notifications: NotificationData[]) => void

export class NotificationService {
  private static notifications: NotificationData[] = []
  private static listeners: Set<NotificationListener> = new Set()
  private static nextId = 1

  /**
   * Show a notification
   */
  static show(notification: Omit<NotificationData, 'id'>): string {
    const id = `notification_${this.nextId++}`
    
    const fullNotification: NotificationData = {
      id,
      duration: 5000, // Default 5 seconds
      position: 'top-right',
      dismissible: true,
      persistent: false,
      priority: 'normal',
      ...notification
    }

    this.notifications.push(fullNotification)
    this.notifyListeners()

    // Auto-dismiss if duration is set
    if (fullNotification.duration && fullNotification.duration > 0) {
      setTimeout(() => {
        this.dismiss(id)
      }, fullNotification.duration)
    }

    // Save persistent notifications
    if (fullNotification.persistent) {
      this.savePersistentNotifications()
    }

    return id
  }

  /**
   * Quick notification methods
   */
  static success(title: string, message: string, actions?: NotificationAction[]): string {
    return this.show({
      type: 'success',
      title,
      message,
      icon: '✅',
      actions,
      gradient: 'from-green-500 to-green-600'
    })
  }

  static error(title: string, message: string, actions?: NotificationAction[]): string {
    return this.show({
      type: 'error',
      title,
      message,
      icon: '❌',
      actions,
      duration: 8000, // Longer for errors
      gradient: 'from-red-500 to-red-600'
    })
  }

  static warning(title: string, message: string, actions?: NotificationAction[]): string {
    return this.show({
      type: 'warning',
      title,
      message,
      icon: '⚠️',
      actions,
      gradient: 'from-yellow-500 to-yellow-600'
    })
  }

  static info(title: string, message: string, actions?: NotificationAction[]): string {
    return this.show({
      type: 'info',
      title,
      message,
      icon: 'ℹ️',
      actions,
      gradient: 'from-blue-500 to-blue-600'
    })
  }

  static promotion(title: string, message: string, actions?: NotificationAction[]): string {
    return this.show({
      type: 'promotion',
      title,
      message,
      icon: '👑',
      actions,
      duration: 0, // No auto-dismiss for promotions
      position: 'center',
      gradient: 'from-purple-500 to-purple-600',
      priority: 'urgent'
    })
  }

  /**
   * Special notification types
   */
  static roleChange(oldRole: string, newRole: string, onRefresh?: () => void): string {
    const isPromotion = newRole === 'admin' && oldRole === 'user'
    const isDemotion = newRole === 'user' && oldRole === 'admin'

    const actions: NotificationAction[] = []
    
    if (onRefresh) {
      actions.push({
        label: 'تحديث الصفحة',
        onClick: onRefresh,
        variant: 'primary'
      })
    }

    actions.push({
      label: 'لاحقاً',
      onClick: () => {},
      variant: 'secondary'
    })

    if (isPromotion) {
      return this.promotion(
        '🎉 تم ترقيتك إلى مدير!',
        'لديك الآن صلاحيات إدارية كاملة. يرجى تحديث الصفحة لرؤية لوحة التحكم الإدارية.',
        actions
      )
    } else if (isDemotion) {
      return this.error(
        '⬇️ تم إلغاء صلاحياتك الإدارية',
        'لم تعد تملك صلاحيات إدارية. سيتم توجيهك للصفحة الرئيسية.',
        actions
      )
    } else {
      return this.info(
        '🔄 تم تغيير دورك',
        `تم تغيير دورك من ${oldRole} إلى ${newRole}`,
        actions
      )
    }
  }

  static accountStatus(status: string, onRefresh?: () => void): string {
    const actions: NotificationAction[] = []
    
    if (onRefresh) {
      actions.push({
        label: 'تحديث',
        onClick: onRefresh,
        variant: 'primary'
      })
    }

    if (status === 'active') {
      return this.success(
        '🎉 تم تفعيل حسابك!',
        'مرحباً بك في نيران كارد. يمكنك الآن استخدام جميع الميزات.',
        actions
      )
    } else if (status === 'suspended') {
      return this.error(
        '⚠️ تم تعليق حسابك',
        'يرجى التواصل مع الدعم الفني لمعرفة السبب.',
        actions
      )
    } else {
      return this.warning(
        '⏳ حسابك قيد المراجعة',
        'سيتم تفعيل حسابك خلال 24-48 ساعة.',
        actions
      )
    }
  }

  static balanceUpdate(amount: number, type: 'credit' | 'debit', newBalance: number): string {
    const isCredit = type === 'credit'
    const formattedAmount = formatCurrencyForNotification(amount)
    const formattedBalance = formatCurrencyForNotification(newBalance)

    return this.show({
      type: isCredit ? 'success' : 'info',
      title: isCredit ? '💰 تم إضافة رصيد' : '💸 تم خصم رصيد',
      message: `${isCredit ? 'تم إضافة' : 'تم خصم'} ${formattedAmount}. رصيدك الحالي: ${formattedBalance}`,
      icon: isCredit ? '💰' : '💸',
      gradient: isCredit ? 'from-green-500 to-green-600' : 'from-blue-500 to-blue-600'
    })
  }

  static orderUpdate(orderId: string, status: string): string {
    const statusMap = {
      'completed': { icon: '✅', title: 'تم إكمال الطلب', type: 'success' as const },
      'cancelled': { icon: '❌', title: 'تم إلغاء الطلب', type: 'error' as const },
      'pending': { icon: '⏳', title: 'الطلب قيد المعالجة', type: 'info' as const }
    }

    const config = statusMap[status as keyof typeof statusMap] || statusMap.pending

    return this.show({
      type: config.type,
      title: config.title,
      message: `طلب رقم: ${orderId}`,
      icon: config.icon
    })
  }

  /**
   * Dismiss a notification
   */
  static dismiss(id: string): void {
    this.notifications = this.notifications.filter(n => n.id !== id)
    this.notifyListeners()
    this.savePersistentNotifications()
  }

  /**
   * Dismiss all notifications
   */
  static dismissAll(): void {
    this.notifications = []
    this.notifyListeners()
    this.savePersistentNotifications()
  }

  /**
   * Get all notifications
   */
  static getAll(): NotificationData[] {
    return [...this.notifications]
  }

  /**
   * Subscribe to notification changes
   */
  static subscribe(listener: NotificationListener): () => void {
    this.listeners.add(listener)
    
    // Return unsubscribe function
    return () => {
      this.listeners.delete(listener)
    }
  }

  /**
   * Notify all listeners
   */
  private static notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener([...this.notifications])
      } catch (error) {
        console.error('Notification listener error:', error)
      }
    })
  }

  /**
   * Save persistent notifications to localStorage
   */
  private static savePersistentNotifications(): void {
    try {
      const persistent = this.notifications.filter(n => n.persistent)
      localStorage.setItem('neran_notifications', JSON.stringify(persistent))
    } catch (error) {
      console.warn('Failed to save persistent notifications:', error)
    }
  }

  /**
   * Load persistent notifications from localStorage
   */
  static loadPersistentNotifications(): void {
    try {
      const saved = localStorage.getItem('neran_notifications')
      if (saved) {
        const persistent: NotificationData[] = JSON.parse(saved)
        this.notifications.push(...persistent)
        this.notifyListeners()
      }
    } catch (error) {
      console.warn('Failed to load persistent notifications:', error)
    }
  }

  /**
   * Clear all persistent notifications
   */
  static clearPersistent(): void {
    try {
      localStorage.removeItem('neran_notifications')
      this.notifications = this.notifications.filter(n => !n.persistent)
      this.notifyListeners()
    } catch (error) {
      console.warn('Failed to clear persistent notifications:', error)
    }
  }
}
