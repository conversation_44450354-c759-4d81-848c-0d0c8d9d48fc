'use client'

import dynamic from "next/dynamic"

// Dynamic imports for client-only components
// Removed ConnectionStatus - Firebase dependency

const Toaster = dynamic(() => import("../../components/ui/toaster").then(mod => ({ default: mod.Toaster })), {
  ssr: false,
  loading: () => null,
})

// Temporarily disabled due to webpack module resolution issue
// const RoleChangeNotification = dynamic(() => import("./RoleChangeNotification"), {
//   ssr: false,
//   loading: () => null,
// })

// Temporarily disabled due to webpack module resolution issue
// const AdminAccessNotification = dynamic(() => import("./AdminAccessNotification"), {
//   ssr: false,
//   loading: () => null,
// })

import UniversalNotifications from "./UniversalNotifications"

/**
 * Client Components Wrapper
 * Handles all client-only components that shouldn't be server-rendered
 */
export default function ClientComponents() {
  return (
    <>
      {/* Removed ConnectionStatus - Firebase dependency */}
      <Toaster />
      {/* <RoleChangeNotification /> */}
      {/* <AdminAccessNotification /> */}
      <UniversalNotifications />
    </>
  )
}
