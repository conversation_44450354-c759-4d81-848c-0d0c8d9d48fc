"use client"

interface PurchaseButtonProps {
  onPurchase: () => void
  disabled: boolean
  loading?: boolean
}

export default function PurchaseButton({ onPurchase, disabled, loading = false }: PurchaseButtonProps) {
  return (
    <button
      onClick={onPurchase}
      disabled={disabled || loading}
      className={`text-white font-bold py-4 px-8 rounded-lg text-xl transition-all duration-200 hover:scale-105 shadow-lg min-w-[200px] flex items-center justify-center gap-2 ${
        disabled || loading ? "opacity-50 cursor-not-allowed" : "hover:shadow-red-500/20"
      }`}
      style={{
        backgroundColor: disabled || loading ? "#666" : "#FF4C4C",
      }}
      onMouseEnter={(e) => {
        if (!disabled && !loading) {
          e.currentTarget.style.backgroundColor = "#e53e3e"
        }
      }}
      onMouseLeave={(e) => {
        if (!disabled && !loading) {
          e.currentTarget.style.backgroundColor = "#FF4C4C"
        }
      }}
    >
      {loading && (
        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
      )}
      {loading ? 'جاري المعالجة...' : 'شحن الآن'}
    </button>
  )
}
