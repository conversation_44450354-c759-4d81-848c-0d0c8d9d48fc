"use client"

import { useState } from "react"
import { Copy, Check } from "lucide-react"

interface CopyButtonProps {
  text: string
  className?: string
  size?: "sm" | "md" | "lg"
}

export default function CopyButton({ text, className = "", size = "sm" }: CopyButtonProps) {
  const [copied, setCopied] = useState(false)

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(text)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      // Fallback for older browsers
      const textArea = document.createElement("textarea")
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand("copy")
      document.body.removeChild(textArea)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    }
  }

  const sizeClasses = {
    sm: "w-3 h-3",
    md: "w-4 h-4",
    lg: "w-5 h-5",
  }

  const buttonSizeClasses = {
    sm: "p-1",
    md: "p-1.5",
    lg: "p-2",
  }

  return (
    <button
      onClick={handleCopy}
      className={`${buttonSizeClasses[size]} hover:bg-gray-700 rounded transition-colors ${className}`}
      title={copied ? "تم النسخ!" : "نسخ"}
    >
      {copied ? (
        <Check className={`${sizeClasses[size]} text-green-400`} />
      ) : (
        <Copy className={`${sizeClasses[size]} text-gray-400 hover:text-white`} />
      )}
    </button>
  )
}
