'use client'

import { Product, User } from '../../types'
import { SSRProduct } from '../../lib/ssr-helpers'
import {
  isOfferActive,
  getPriceDisplayInfo,
  getOfferBadgeText,
  isOfferExpiringSoon,
  getEffectivePriceByRole,
  getRoleBasedPrice
} from '../../lib/utils/offers'
import { formatCurrency } from '../../lib/utils/currency'

// Union type to handle both Product and SSRProduct
type ProductType = Product | SSRProduct

interface ProductCardsProps {
  products: ProductType[]
  selectedProduct: ProductType | null
  setSelectedProduct: (product: ProductType | null) => void
  user?: User | null
}

export default function ProductCards({ products, selectedProduct, setSelectedProduct, user }: ProductCardsProps) {
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'gems':
      case 'pubg':
        return '💎'
      case 'membership':
        return '👑'
      case 'pass':
        return '🎫'
      case 'freefire':
        return '🔥'
      case 'fortnite':
        return '⚡'
      default:
        return '🎮'
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'gems':
      case 'pubg':
        return 'from-blue-600 to-blue-800'
      case 'membership':
        return 'from-purple-600 to-purple-800'
      case 'pass':
        return 'from-green-600 to-green-800'
      case 'freefire':
        return 'from-red-600 to-red-800'
      case 'fortnite':
        return 'from-yellow-600 to-yellow-800'
      default:
        return 'from-gray-600 to-gray-800'
    }
  }

  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-lg">لا توجد منتجات متاحة حال</div>
        <div className="text-gray-500 text-sm mt-2">يرجى المحاولة مرة أخرى لاحق</div>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4">
      {products.map((product) => {
        // Get role-based price info
        const effectivePrice = getEffectivePriceByRole(product, user?.role || 'user')
        const roleBasedPrice = getRoleBasedPrice(product, user?.role || 'user')
        const priceInfo = getPriceDisplayInfo(product)
        const offerBadge = getOfferBadgeText(product)
        const isExpiringSoon = isOfferExpiringSoon(product)

        return (
          <button
            key={product.id}
            onClick={() => setSelectedProduct(product)}
            className={`relative p-3 sm:p-4 rounded-lg border-2 transition-all duration-200 text-right hover:scale-105 group min-h-[120px] sm:min-h-[140px] ${
              selectedProduct?.id === product.id
                ? 'border-red-500 shadow-lg shadow-red-500/20'
                : 'border-gray-600 hover:border-red-800'
            }`}
            style={{
              backgroundColor: selectedProduct?.id === product.id ? 'rgba(239, 68, 68, 0.1)' : '#2a2a2a',
            }}
          >
            {/* Category Badge */}
            <div className={`absolute top-1.5 left-1.5 sm:top-2 sm:left-2 px-1.5 py-0.5 sm:px-2 sm:py-1 rounded-full text-xs font-medium bg-gradient-to-r ${getCategoryColor(product.category)} text-white`}>
              {getCategoryIcon(product.category)}
            </div>

            {/* Offer Badge */}
            {offerBadge && (
              <div className={`absolute top-1.5 right-1.5 sm:top-2 sm:right-2 px-1.5 py-0.5 sm:px-2 sm:py-1 rounded-full text-xs font-bold ${
                isExpiringSoon
                  ? 'bg-gradient-to-r from-orange-500 to-red-600 text-white animate-pulse'
                  : 'bg-gradient-to-r from-green-500 to-green-700 text-white'
              }`}>
                {offerBadge}
              </div>
            )}

            {/* Product Content */}
            <div className="space-y-2 sm:space-y-3 mt-4 sm:mt-6">
              {/* Price Section */}
              <div className="space-y-1">
                {priceInfo.hasActiveOffer && priceInfo.originalPrice ? (
                  <>
                    {/* Original Price (Strikethrough) */}
                    <div className="text-sm text-gray-400 line-through">
                      {formatCurrency(priceInfo.originalPrice)}
                    </div>
                    {/* Discounted Price */}
                    <div className="text-lg sm:text-xl font-bold text-green-400">
                      {formatCurrency(effectivePrice)}
                    </div>
                  </>
                ) : (
                  /* Role-based Price */
                  <div className="space-y-1">
                    <div className="text-lg sm:text-xl font-bold text-red-400">
                      {formatCurrency(effectivePrice)}
                    </div>
                    {/* Show distributor pricing indicator */}
                    {user?.role === 'distributor' && roleBasedPrice !== product.price && (
                      <div className="text-xs text-orange-400">
                        سعر الموزع
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Product Name */}
              <div className="text-white font-semibold text-xs sm:text-sm leading-tight">
                {product.name}
              </div>

              {/* Description */}
              {product.description && (
                <div className="text-gray-400 text-xs leading-tight line-clamp-2">
                  {product.description}
                </div>
              )}
            </div>

            {/* Selection Indicator */}
            {selectedProduct?.id === product.id && (
              <div className="absolute inset-0 rounded-lg border-2 border-red-500 bg-red-500/5 flex items-center justify-center">
                <div className="bg-red-500 text-white rounded-full w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center text-xs sm:text-sm font-bold">
                  ✓
                </div>
              </div>
            )}

            {/* Hover Effect */}
            <div className="absolute inset-0 rounded-lg bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
          </button>
        )
      })}
    </div>
  )
}

