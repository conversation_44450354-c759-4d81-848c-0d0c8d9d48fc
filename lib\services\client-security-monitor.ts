/**
 * Client-Side Security Monitor
 * Detects and prevents client-side manipulation attempts
 */

import { User } from '../../types'
import { toast } from '../../hooks/use-toast'
import { NotificationService } from './notification-service'

interface SecurityEvent {
  type: 'localStorage_manipulation' | 'console_access' | 'devtools_open' | 'data_tampering'
  timestamp: number
  details: any
}

export class ClientSecurityMonitor {
  private static isInitialized = false
  private static securityEvents: SecurityEvent[] = []
  private static originalConsole: any = {}
  private static lastKnownUserData: User | null = null

  /**
   * Initialize security monitoring
   */
  static initialize(): void {
    if (this.isInitialized || typeof window === 'undefined') return

    // Disable in development to prevent issues
    if (process.env.NODE_ENV === 'development') {
      console.log('🔓 Security monitoring disabled in development mode')
      return
    }

    console.log('🔒 Initializing Client Security Monitor...')

    try {
      this.setupLocalStorageMonitoring()
      this.setupConsoleMonitoring()
      this.setupDevToolsDetection()
      this.setupDataIntegrityChecks()

      this.isInitialized = true
    } catch (error) {
      console.error('Failed to initialize security monitor:', error)
      // Don't throw error to avoid breaking the app
    }
  }

  /**
   * Monitor localStorage for unauthorized changes
   */
  private static setupLocalStorageMonitoring(): void {
    const originalSetItem = localStorage.setItem
    const originalRemoveItem = localStorage.removeItem
    const originalClear = localStorage.clear

    // Override setItem to monitor changes
    localStorage.setItem = function(key: string, value: string) {
      if (key === 'neran_user' || key === 'neran_user_timestamp') {
        try {
          // Validate the data being set
          if (key === 'neran_user') {
            const userData = JSON.parse(value)
            ClientSecurityMonitor.validateUserData(userData)
          }
        } catch (error) {
          ClientSecurityMonitor.logSecurityEvent('localStorage_manipulation', {
            key,
            value: value.substring(0, 100), // Log first 100 chars only
            error: error instanceof Error ? error.message : 'Unknown error'
          })
          
          // Prevent the manipulation
          NotificationService.error(
            '🚨 تحذير أمني',
            'تم اكتشاف محاولة تلاعب بالبيانات. سيتم تسجيل خروجك للحماية.',
            [{
              label: 'فهمت',
              onClick: () => window.location.href = '/auth',
              variant: 'primary'
            }]
          )

          // Force logout
          setTimeout(() => {
            window.location.href = '/auth'
          }, 3000)
          
          return
        }
      }
      
      originalSetItem.call(this, key, value)
    }

    // Override removeItem to monitor deletions
    localStorage.removeItem = function(key: string) {
      if (key === 'neran_user' || key === 'neran_user_timestamp') {
        ClientSecurityMonitor.logSecurityEvent('localStorage_manipulation', {
          action: 'remove',
          key
        })
      }
      
      originalRemoveItem.call(this, key)
    }

    // Override clear to monitor mass deletions
    localStorage.clear = function() {
      ClientSecurityMonitor.logSecurityEvent('localStorage_manipulation', {
        action: 'clear'
      })
      
      originalClear.call(this)
    }
  }

  /**
   * Monitor console access for suspicious activity (disabled in production)
   */
  private static setupConsoleMonitoring(): void {
    // Disabled in production for performance
    if (process.env.NODE_ENV === 'production') {
      return
    }

    // Store original console methods
    this.originalConsole = {
      log: console.log,
      warn: console.warn,
      error: console.error
    }

    // Monitor console usage patterns (development only)
    let consoleUsageCount = 0
    const consoleUsageThreshold = 100 // Higher threshold for development

    const wrapConsoleMethod = (method: string) => {
      const original = (console as any)[method]
      ;(console as any)[method] = function(...args: any[]) {
        // Avoid monitoring our own security logs to prevent circular dependency
        const isSecurityLog = args.some(arg =>
          typeof arg === 'string' && arg.includes('🚨 Security Event')
        )

        if (!isSecurityLog) {
          consoleUsageCount++

          // Check for suspicious console usage (much higher threshold)
          if (consoleUsageCount > consoleUsageThreshold) {
            // Use setTimeout to break potential circular calls
            setTimeout(() => {
              ClientSecurityMonitor.logSecurityEvent('console_access', {
                method,
                count: consoleUsageCount,
                args: args.slice(0, 2).map(arg =>
                  typeof arg === 'string' ? arg.substring(0, 50) : typeof arg
                )
              })
            }, 0)
          }
        }

        return original.apply(console, args)
      }
    }

    wrapConsoleMethod('log')
    wrapConsoleMethod('warn')
    wrapConsoleMethod('error')
  }

  /**
   * Detect developer tools opening (optimized for production)
   */
  private static setupDevToolsDetection(): void {
    // Disabled in production for performance
    if (process.env.NODE_ENV === 'production') {
      return
    }

    let devtools = { open: false, orientation: null }

    // Reduced frequency for better performance
    setInterval(() => {
      if (window.outerHeight - window.innerHeight > 200 ||
          window.outerWidth - window.innerWidth > 200) {
        if (!devtools.open) {
          devtools.open = true
          this.logSecurityEvent('devtools_open', {
            timestamp: Date.now(),
            userAgent: navigator.userAgent
          })
        }
      } else {
        devtools.open = false
      }
    }, 5000) // Reduced from 500ms to 5 seconds
  }

  /**
   * Setup data integrity checks (optimized for production)
   */
  private static setupDataIntegrityChecks(): void {
    // Disabled in production for performance
    if (process.env.NODE_ENV === 'production') {
      return
    }

    // Reduced frequency for better performance
    setInterval(() => {
      this.performDataIntegrityCheck()
    }, 300000) // Reduced from 30 seconds to 5 minutes
  }

  /**
   * Validate user data for tampering
   */
  private static validateUserData(userData: any): void {
    // Check for required fields
    const requiredFields = ['id', 'email', 'name', 'role', 'status', 'balance']
    for (const field of requiredFields) {
      if (userData[field] === undefined) {
        throw new Error(`Missing required field: ${field}`)
      }
    }

    // Validate data types
    if (typeof userData.balance !== 'number' || userData.balance < 0) {
      throw new Error('Invalid balance value')
    }

    if (!['user', 'admin'].includes(userData.role)) {
      throw new Error('Invalid role value')
    }

    if (!['active', 'pending', 'suspended'].includes(userData.status)) {
      throw new Error('Invalid status value')
    }

    // Check for suspicious values
    if (userData.balance > 1000000) { // Suspiciously high balance
      throw new Error('Suspicious balance value')
    }

    // Compare with last known data
    if (this.lastKnownUserData) {
      const balanceDiff = Math.abs(userData.balance - this.lastKnownUserData.balance)
      if (balanceDiff > 10000) { // Large balance change
        throw new Error('Suspicious balance change')
      }

      if (userData.role !== this.lastKnownUserData.role && userData.role === 'admin') {
        // Role change to admin should be verified
        this.logSecurityEvent('data_tampering', {
          field: 'role',
          oldValue: this.lastKnownUserData.role,
          newValue: userData.role
        })
      }
    }

    // Update last known data
    this.lastKnownUserData = { ...userData }
  }

  /**
   * Perform comprehensive data integrity check
   */
  private static performDataIntegrityCheck(): void {
    try {
      const userData = localStorage.getItem('neran_user')
      if (userData) {
        const parsedData = JSON.parse(userData)
        this.validateUserData(parsedData)
      }
    } catch (error) {
      this.logSecurityEvent('data_tampering', {
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now()
      })

      // Clear potentially corrupted data
      localStorage.removeItem('neran_user')
      localStorage.removeItem('neran_user_timestamp')

      NotificationService.error(
        '🚨 تحذير أمني',
        'تم اكتشاف تلاعب بالبيانات. سيتم تسجيل خروجك للحماية.',
        [{
          label: 'فهمت',
          onClick: () => window.location.href = '/auth',
          variant: 'primary'
        }]
      )

      setTimeout(() => {
        window.location.href = '/auth'
      }, 3000)
    }
  }

  /**
   * Log security event
   */
  private static logSecurityEvent(type: SecurityEvent['type'], details: any): void {
    const event: SecurityEvent = {
      type,
      timestamp: Date.now(),
      details
    }

    this.securityEvents.push(event)

    // Keep only last 100 events
    if (this.securityEvents.length > 100) {
      this.securityEvents = this.securityEvents.slice(-100)
    }

    // Use original console.warn to avoid circular dependency
    if (this.originalConsole.warn) {
      this.originalConsole.warn(`🚨 Security Event: ${type}`, details)
    }

    // Send to server for logging (in production)
    this.reportSecurityEvent(event)
  }

  /**
   * Report security event to server
   */
  private static async reportSecurityEvent(event: SecurityEvent): Promise<void> {
    // Don't report in development
    if (process.env.NODE_ENV === 'development') {
      return
    }

    try {
      // Use a timeout to prevent hanging requests
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 5000)

      await fetch('/api/security/log', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(event),
        signal: controller.signal
      })

      clearTimeout(timeoutId)
    } catch (error) {
      // Use original console to avoid circular dependency
      if (this.originalConsole.warn) {
        this.originalConsole.warn('Failed to report security event:', error)
      }
    }
  }

  /**
   * Get security events (for debugging)
   */
  static getSecurityEvents(): SecurityEvent[] {
    return [...this.securityEvents]
  }

  /**
   * Clear security events
   */
  static clearSecurityEvents(): void {
    this.securityEvents = []
  }

  /**
   * Disable security monitoring (for development)
   */
  static disable(): void {
    if (process.env.NODE_ENV === 'development') {
      this.isInitialized = false
      console.log('🔓 Security monitoring disabled for development')
    }
  }

  /**
   * Check if monitoring is active
   */
  static isActive(): boolean {
    return this.isInitialized
  }
}
