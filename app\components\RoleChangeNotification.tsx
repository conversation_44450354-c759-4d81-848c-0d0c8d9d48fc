'use client'

import { useEffect, useState } from 'react'
import { Crown, RefreshCw, X } from 'lucide-react'
import { useSupabaseAuth } from '../../contexts/SupabaseAuthContext'

export default function RoleChangeNotification() {
  const { user, refreshUserData, forceRefreshWithReload } = useSupabaseAuth()
  const [showNotification, setShowNotification] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [lastKnownRole, setLastKnownRole] = useState<string | null>(null)

  useEffect(() => {
    if (!user) return

    // Check if role has changed
    if (lastKnownRole && lastKnownRole !== user.role) {
      if (user.role === 'admin' && lastKnownRole === 'user') {
        setShowNotification(true)
      }
    }

    setLastKnownRole(user.role)
  }, [user?.role, lastKnownRole])

  const handleRefresh = async () => {
    setIsRefreshing(true)
    try {
      await refreshUserData()
      // Small delay then force reload to ensure admin UI loads
      setTimeout(() => {
        window.location.reload()
      }, 1000)
    } catch (error) {
      console.error('Error refreshing user data:', error)
      window.location.reload()
    }
  }

  const handleForceRefresh = async () => {
    setIsRefreshing(true)
    await forceRefreshWithReload()
  }

  if (!showNotification || !user) return null

  return (
    <div className="fixed top-20 left-4 right-4 z-50 max-w-md mx-auto">
      <div className="bg-gradient-to-r from-purple-600 to-purple-700 border border-purple-500 rounded-lg p-4 shadow-2xl">
        <div className="flex items-start gap-3">
          <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
            <Crown className="w-4 h-4 text-white" />
          </div>
          
          <div className="flex-1">
            <h3 className="text-white font-semibold mb-1">
              🎉 تم ترقيتك إلى مدير!
            </h3>
            <p className="text-purple-100 text-sm mb-3">
              لديك الآن صلاحيات إدارية كاملة. يرجى تحديث الصفحة لرؤية لوحة التحكم الإدارية.
            </p>
            
            <div className="flex gap-2">
              <button
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="bg-white text-purple-700 px-3 py-1.5 rounded text-sm font-medium hover:bg-purple-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1"
              >
                <RefreshCw className={`w-3 h-3 ${isRefreshing ? 'animate-spin' : ''}`} />
                {isRefreshing ? 'جاري التحديث...' : 'تحديث الصفحة'}
              </button>
              
              <button
                onClick={() => setShowNotification(false)}
                className="text-purple-200 hover:text-white px-2 py-1.5 rounded text-sm"
              >
                لاحقاً
              </button>
            </div>
          </div>
          
          <button
            onClick={() => setShowNotification(false)}
            className="text-purple-200 hover:text-white p-1"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  )
}
