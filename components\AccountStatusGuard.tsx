"use client"

import { useEffect, useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { Clock, XCircle, Shield, Mail, Phone, RefreshCw, MessageCircle } from "lucide-react"
import { useSupabaseAuth } from "../contexts/SupabaseAuthContext"

interface AccountStatusGuardProps {
  children: React.ReactNode
}

export default function AccountStatusGuard({ children }: AccountStatusGuardProps) {
  const router = useRouter()
  const pathname = usePathname()
  const { user: authUser, loading: authLoading, initialized } = useSupabaseAuth()
  const [isClient, setIsClient] = useState(false)
  const [redirecting, setRedirecting] = useState(false)

  console.log(`🔍 AccountStatusGuard MOUNTED: pathname=${pathname}`)

  // ✅ ALL HOOKS MUST BE CALLED FIRST - BEFORE ANY CONDITIONAL LOGIC
  
  // Handle client-side hydration
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Handle redirect to auth when no user (using useEffect to avoid render-time navigation)
  useEffect(() => {
    if (isClient && !authLoading && initialized && !authUser && !pathname.startsWith('/auth') && pathname !== '/' && !redirecting) {
      console.log(`🔐 AccountStatusGuard: No user, redirecting to auth`)
      setRedirecting(true)
      router.push('/auth')
    }
  }, [isClient, authLoading, initialized, authUser, pathname, router, redirecting])

  console.log(`🔍 AccountStatusGuard: user=${authUser ? `${authUser.name}(${authUser.status})` : 'null'}, loading=${authLoading}, initialized=${initialized}, isClient=${isClient}`)

  // During SSR or before hydration, render children to avoid hydration mismatch
  if (!isClient) {
    return <>{children}</>
  }

  // Show loading while auth is initializing (only on client)
  if (authLoading || !initialized) {
    console.log(`⏳ AccountStatusGuard: Showing loading screen for ${pathname} (auth loading: ${authLoading}, initialized: ${initialized})`)
    return <LoadingScreen />
  }

  // If redirecting or no user on protected pages, show loading
  if (redirecting || (!authUser && !pathname.startsWith('/auth') && pathname !== '/')) {
    return <LoadingScreen />
  }

  // COMPLETE BLOCKING: Show full-screen overlay for pending/suspended users
  if (authUser && (authUser.status === 'pending' || authUser.status === 'suspended')) {
    console.log(`🚫 AccountStatusGuard: BLOCKING ${authUser.status} user ${authUser.name} from accessing website`)
    return <AccountStatusBlockingOverlay user={authUser} />
  }

  // Active users or no user (on allowed pages) can access everything
  console.log(`✅ AccountStatusGuard: Allowing access to ${pathname}`)
  return <>{children}</>
}

// Loading Screen Component
function LoadingScreen() {
  return (
    <div className="fixed inset-0 z-[9999] bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center p-4">
      <div className="text-center">
        <div className="w-12 h-12 sm:w-16 sm:h-16 border-4 border-red-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <div className="text-white text-lg sm:text-xl font-medium">جاري التحميل...</div>
        <div className="text-gray-400 text-sm sm:text-base mt-2">يتم فحص حالة الحساب...</div>
      </div>
    </div>
  )
}

// Full-Screen Account Status Blocking Overlay
function AccountStatusBlockingOverlay({ user }: { user: any }) {
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [whatsAppNumber, setWhatsAppNumber] = useState<string>("+************")
  const { refreshUserData } = useSupabaseAuth()

  // Fetch WhatsApp number from admin settings
  useEffect(() => {
    const fetchWhatsAppNumber = async () => {
      try {
        const response = await fetch('/api/settings')
        if (response.ok) {
          const settings = await response.json()
          if (settings && settings.supportWhatsApp) {
            setWhatsAppNumber(settings.supportWhatsApp)
          }
        }
      } catch (error) {
        console.warn('Could not fetch WhatsApp number from settings, using default:', error)
      }
    }

    fetchWhatsAppNumber()
  }, [])

  const handleRefreshStatus = async () => {
    setIsRefreshing(true)
    try {
      console.log(`🔄 Refreshing status for user ${user.id}`)

      // Use the auth context's refresh method instead of direct API call
      const updatedUser = await refreshUserData()

      if (updatedUser) {
        console.log(`✅ Updated user status:`, updatedUser.status)

        // If status changed to active, the auth context will handle the update
        if (updatedUser.status === 'active') {
          // Small delay then reload to ensure state is updated
          setTimeout(() => {
            window.location.reload()
          }, 500)
        }
      }
    } catch (error) {
      console.error('Error refreshing status:', error)
    } finally {
      setIsRefreshing(false)
    }
  }

  const handleWhatsAppContact = () => {
    const isPending = user.status === 'pending'
    const message = isPending 
      ? `🔥 *طلب تفعيل حساب - نيران كارد*\n\n` +
        `👤 *معرف المستخدم:* ${user.id}\n` +
        `📧 *الاسم:* ${user.name}\n` +
        `📧 *البريد الإلكتروني:* ${user.email}\n\n` +
        `مرحباً، أريد تفعيل حسابي الجديد بأسرع وقت ممكن.\n\n` +
        `شكراً لكم 🙏`
      : `🚨 *استفسار حول تعليق الحساب - نيران كارد*\n\n` +
        `👤 *معرف المستخدم:* ${user.id}\n` +
        `📧 *الاسم:* ${user.name}\n` +
        `📧 *البريد الإلكتروني:* ${user.email}\n\n` +
        `مرحباً، حسابي معلق وأريد معرفة السبب وكيفية إعادة تفعيله.\n\n` +
        `شكراً لكم 🙏`

    const phoneNumber = whatsAppNumber.replace(/\+/g, '')
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`
    window.open(whatsappUrl, '_blank')
  }

  const isPending = user.status === 'pending'
  const isSuspended = user.status === 'suspended'

  return (
    <div className="fixed inset-0 z-[9999] bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center p-4">
      <div className="max-w-2xl w-full bg-gray-800 border border-gray-600 rounded-2xl shadow-2xl overflow-hidden">
        {/* Header */}
        <div className={`px-4 sm:px-6 lg:px-8 py-6 text-center ${
          isPending ? 'bg-gradient-to-r from-yellow-600 to-orange-600' : 'bg-gradient-to-r from-red-600 to-red-700'
        }`}>
          <div className="flex items-center justify-center mb-4">
            {isPending ? (
              <Clock className="w-10 h-10 sm:w-12 sm:h-12 text-white" />
            ) : (
              <Shield className="w-10 h-10 sm:w-12 sm:h-12 text-white" />
            )}
          </div>
          <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-white mb-2">
            {isPending ? 'حسابك قيد المراجعة' : 'تم تعليق حسابك'}
          </h1>
        </div>

        {/* Content */}
        <div className="px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
          {/* User Info */}
          <div className="bg-gray-900 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                {user.name?.charAt(0)?.toUpperCase() || 'ن'}
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-white font-medium text-base sm:text-lg truncate">{user.name}</div>
                <div className="text-gray-400 text-sm sm:text-base truncate">{user.email}</div>
              </div>
            </div>
            <div className={`inline-flex items-center gap-2 px-3 py-2 rounded-full text-sm sm:text-base font-medium ${
              isPending ? 'bg-yellow-600 text-white' : 'bg-red-600 text-white'
            }`}>
              {isPending ? (
                <>
                  <Clock className="w-4 h-4" />
                  معلق
                </>
              ) : (
                <>
                  <XCircle className="w-4 h-4" />
                  محظور
                </>
              )}
            </div>
          </div>

          {/* Status Message */}
          <div className="text-center mb-6 sm:mb-8">
            {isPending ? (
              <div>
                <h2 className="text-lg sm:text-xl lg:text-2xl font-semibold text-white mb-4">
                  حسابك في انتظار موافقة الإدارة
                </h2>
                <p className="text-gray-300 leading-relaxed text-sm sm:text-base lg:text-lg px-2">
                  شكراً لك على التسجيل في منصة نيران كارد. حسابك حالياً قيد المراجعة من قبل فريق الإدارة.
                  سيتم تفعيل حسابك خلال 24-48 ساعة من التسجيل.
                </p>
              </div>
            ) : (
              <div>
                <h2 className="text-lg sm:text-xl lg:text-2xl font-semibold text-white mb-4">
                  تم تعليق حسابك
                </h2>
                <p className="text-gray-300 leading-relaxed text-sm sm:text-base lg:text-lg px-2">
                  تم تعليق حسابك من قبل الإدارة. إذا كنت تعتقد أن هذا خطأ،
                  يرجى التواصل مع فريق الدعم للحصول على المساعدة.
                </p>
              </div>
            )}
          </div>

          {/* Contact Information */}
          <div className="bg-gray-900 rounded-lg p-4 sm:p-6 mb-6">
            <h3 className="text-base sm:text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <Mail className="w-5 h-5 text-red-500" />
              معلومات التواصل
            </h3>
            <div className="space-y-3 mb-4">
              <div className="flex items-start gap-3 text-gray-300">
                <Mail className="w-4 h-4 text-red-500 mt-1 flex-shrink-0" />
                <div className="text-sm sm:text-base">
                  <div className="font-medium">البريد الإلكتروني:</div>
                  <div className="text-red-400"><EMAIL></div>
                </div>
              </div>
              <div className="flex items-start gap-3 text-gray-300">
                <Phone className="w-4 h-4 text-red-500 mt-1 flex-shrink-0" />
                <div className="text-sm sm:text-base">
                  <div className="font-medium">الهاتف:</div>
                  <div className="text-red-400">{whatsAppNumber}</div>
                </div>
              </div>
            </div>

            {/* WhatsApp Contact Button */}
            <button
              onClick={handleWhatsAppContact}
              className="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg transition-colors font-medium flex items-center justify-center gap-2 text-sm sm:text-base"
            >
              <MessageCircle className="w-5 h-5" />
              {isPending ? 'تسريع التفعيل عبر واتساب' : 'تواصل عبر واتساب'}
            </button>
          </div>

          {/* Refresh Button */}
          <div className="text-center">
            <button
              onClick={handleRefreshStatus}
              disabled={isRefreshing}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white px-4 sm:px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2 mx-auto disabled:cursor-not-allowed text-sm sm:text-base"
            >
              <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              {isRefreshing ? 'جاري التحديث...' : 'تحديث الحالة'}
            </button>
            <p className="text-gray-400 text-xs sm:text-sm mt-2 px-2">
              اضغط للتحقق من تحديث حالة حسابك
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="px-4 sm:px-6 lg:px-8 py-4 bg-gray-900 border-t border-gray-700">
          <p className="text-center text-gray-400 text-xs sm:text-sm">
            جميع الحقوق محفوظة © 2025 نيران كارد - إبراهيم الشحيمه
          </p>
        </div>
      </div>
    </div>
  )
}


