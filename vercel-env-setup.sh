#!/bin/bash
# Script to set up Vercel environment variables for production

echo "Setting up Vercel environment variables..."

# Set the SUPABASE_SERVICE_ROLE_KEY for all environments
vercel env add SUPABASE_SERVICE_ROLE_KEY production
# When prompted, paste: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR0amN6dnZmc2VoaXBjdm1vdWR5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTc3MjYzMiwiZXhwIjoyMDY1MzQ4NjMyfQ.o4oKPgQQKJW5clWvLvxhdkon39Q4ucaOXtwGdExMRxE

vercel env add SUPABASE_SERVICE_ROLE_KEY preview
# When prompted, paste the same key

echo "Environment variables set. Now redeploy your application:"
echo "vercel --prod"
