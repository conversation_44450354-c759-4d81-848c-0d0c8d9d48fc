/**
 * Hook for using notifications anywhere in the app
 */

import { NotificationService, NotificationAction } from '../lib/services/notification-service'

export function useNotifications() {
  return {
    // Basic notification methods
    show: NotificationService.show.bind(NotificationService),
    success: NotificationService.success.bind(NotificationService),
    error: NotificationService.error.bind(NotificationService),
    warning: NotificationService.warning.bind(NotificationService),
    info: NotificationService.info.bind(NotificationService),
    promotion: NotificationService.promotion.bind(NotificationService),

    // Special notification methods
    roleChange: NotificationService.roleChange.bind(NotificationService),
    accountStatus: NotificationService.accountStatus.bind(NotificationService),
    balanceUpdate: NotificationService.balanceUpdate.bind(NotificationService),
    orderUpdate: NotificationService.orderUpdate.bind(NotificationService),

    // Control methods
    dismiss: NotificationService.dismiss.bind(NotificationService),
    dismissAll: NotificationService.dismissAll.bind(NotificationService),
    getAll: NotificationService.getAll.bind(NotificationService),

    // Quick notification helpers
    showSuccess: (message: string, actions?: NotificationAction[]) => 
      NotificationService.success('نجح!', message, actions),
    
    showError: (message: string, actions?: NotificationAction[]) => 
      NotificationService.error('خطأ!', message, actions),
    
    showWarning: (message: string, actions?: NotificationAction[]) => 
      NotificationService.warning('تحذير!', message, actions),
    
    showInfo: (message: string, actions?: NotificationAction[]) => 
      NotificationService.info('معلومة', message, actions),

    // Loading notification
    showLoading: (message: string = 'جاري التحميل...') =>
      NotificationService.show({
        type: 'info',
        title: '⏳ يرجى الانتظار',
        message,
        icon: '⏳',
        duration: 0, // No auto-dismiss
        dismissible: true, // Allow manual dismissal
        gradient: 'from-blue-500 to-blue-600'
      }),

    // Welcome notification
    showWelcome: (userName: string) => 
      NotificationService.success(
        `مرحباً ${userName}! 👋`,
        'أهلاً وسهلاً بك في نيران كارد'
      ),

    // Logout notification
    showLogout: () => 
      NotificationService.info(
        'تم تسجيل الخروج',
        'نراك قريباً! 👋'
      ),

    // Network error notification
    showNetworkError: (retry?: () => void) => 
      NotificationService.error(
        'مشكلة في الاتصال',
        'يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى',
        retry ? [{
          label: 'إعادة المحاولة',
          onClick: retry,
          variant: 'primary' as const
        }] : undefined
      ),

    // Maintenance notification
    showMaintenance: (endTime?: string) => 
      NotificationService.warning(
        '🔧 صيانة مجدولة',
        endTime 
          ? `الموقع تحت الصيانة حتى ${endTime}`
          : 'الموقع تحت الصيانة حالياً',
        [{
          label: 'تحديث',
          onClick: () => window.location.reload(),
          variant: 'primary' as const
        }]
      ),

    // Update available notification
    showUpdateAvailable: () => 
      NotificationService.info(
        '🆕 تحديث متوفر',
        'يوجد إصدار جديد من الموقع',
        [{
          label: 'تحديث الآن',
          onClick: () => window.location.reload(),
          variant: 'primary' as const
        }, {
          label: 'لاحقاً',
          onClick: () => {},
          variant: 'secondary' as const
        }]
      ),

    // Cookie consent notification
    showCookieConsent: (onAccept: () => void, onDecline: () => void) => 
      NotificationService.show({
        type: 'info',
        title: '🍪 ملفات تعريف الارتباط',
        message: 'نستخدم ملفات تعريف الارتباط لتحسين تجربتك',
        position: 'bottom-center',
        duration: 0,
        persistent: true,
        actions: [{
          label: 'موافق',
          onClick: onAccept,
          variant: 'primary' as const
        }, {
          label: 'رفض',
          onClick: onDecline,
          variant: 'secondary' as const
        }]
      }),

    // Custom positioned notifications
    showTopCenter: (title: string, message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') =>
      NotificationService.show({
        type,
        title,
        message,
        position: 'top-center'
      }),

    showBottomRight: (title: string, message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') =>
      NotificationService.show({
        type,
        title,
        message,
        position: 'bottom-right'
      }),

    showCenter: (title: string, message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') =>
      NotificationService.show({
        type,
        title,
        message,
        position: 'center',
        duration: 0 // Center notifications usually need manual dismissal
      }),

    // Bulk operations
    showBulkSuccess: (count: number, operation: string) =>
      NotificationService.success(
        'تمت العملية بنجاح',
        `تم ${operation} ${count} عنصر بنجاح`
      ),

    showBulkError: (count: number, operation: string) =>
      NotificationService.error(
        'فشلت العملية',
        `فشل في ${operation} ${count} عنصر`
      ),

    // Form notifications
    showFormSuccess: (formName: string) =>
      NotificationService.success(
        'تم الحفظ بنجاح',
        `تم حفظ ${formName} بنجاح`
      ),

    showFormError: (errors: string[]) =>
      NotificationService.error(
        'خطأ في النموذج',
        `يرجى تصحيح الأخطاء التالية: ${errors.join(', ')}`
      ),

    // File upload notifications
    showUploadProgress: (fileName: string, progress: number) =>
      NotificationService.show({
        type: 'info',
        title: '📤 جاري الرفع',
        message: `${fileName} - ${progress}%`,
        duration: 0,
        dismissible: false
      }),

    showUploadSuccess: (fileName: string) =>
      NotificationService.success(
        'تم الرفع بنجاح',
        `تم رفع ${fileName} بنجاح`
      ),

    showUploadError: (fileName: string, error: string) =>
      NotificationService.error(
        'فشل في الرفع',
        `فشل في رفع ${fileName}: ${error}`
      )
  }
}
