/**
 * Telegram Bot Service for Order Notifications
 * Sends formatted notifications to admin when new orders are created
 */

interface TelegramMessage {
  orderId: string
  userName: string
  userEmail: string
  productName: string
  gameId: string
  amount: string
  price: number
  status: string
  createdAt: string
  adminUrl: string
}

export class TelegramBotService {
  private static botToken = process.env.TELEGRAM_BOT_TOKEN
  
  // 🚀 DYNAMIC: Auto-detect production URL from Vercel environment
  private static getBaseUrl(): string {
    // Priority 1: Custom domain from environment
    if (process.env.NEXT_PUBLIC_BASE_URL) {
      return process.env.NEXT_PUBLIC_BASE_URL
    }
    
    // Priority 2: Vercel deployment URL (auto-detected)
    if (process.env.VERCEL_URL) {
      return `https://${process.env.VERCEL_URL}`
    }
    
    // Priority 3: Vercel project URL (auto-detected)
    if (process.env.VERCEL_PROJECT_PRODUCTION_URL) {
      return `https://${process.env.VERCEL_PROJECT_PRODUCTION_URL}`
    }
    
    // Fallback: Latest known production URL
    return 'https://neran-card-9mdom8hw5-altyb-apps.vercel.app'
  }
  
  // 👥 MULTIPLE ADMINS: Support multiple admin chat IDs for notifications
  private static getAdminChatIds(): string[] {
    const chatIds: string[] = []
    
    // Method 1: Single admin chat ID
    if (process.env.TELEGRAM_ADMIN_CHAT_ID) {
      chatIds.push(process.env.TELEGRAM_ADMIN_CHAT_ID)
    }
    
    // Method 2: Multiple admin chat IDs (comma-separated)
    // Example: TELEGRAM_ADMIN_CHAT_IDS=123456789,987654321,555666777
    if (process.env.TELEGRAM_ADMIN_CHAT_IDS) {
      const additionalIds = process.env.TELEGRAM_ADMIN_CHAT_IDS
        .split(',')
        .map(id => id.trim())
        .filter(id => id.length > 0)
      chatIds.push(...additionalIds)
    }
    
    // Method 3: Fallback hardcoded admin IDs (for reliability)
    // You can add your chat IDs here as backup
    const fallbackIds = ['7047335419', '7103752512']
    fallbackIds.forEach(id => {
      if (!chatIds.includes(id)) {
        chatIds.push(id)
      }
    })
    
    // Remove duplicates
    return [...new Set(chatIds)]
  }

  /**
   * Send new order notification to all admins
   */
  static async sendOrderNotification(orderData: {
    id: string
    user_name: string
    user_email: string
    product_name: string
    game_id: string
    amount: string
    price: number
    status: string
    created_at: string
  }): Promise<boolean> {
    try {
      if (!this.botToken) {
        console.warn('⚠️ Telegram bot token missing - notification skipped')
        return false
      }

      const chatIds = this.getAdminChatIds()
      if (chatIds.length === 0) {
        console.warn('⚠️ No admin chat IDs configured - notification skipped')
        return false
      }

      // Create direct admin link to the specific order
      const adminUrl = `${this.getBaseUrl()}/admin?section=orders&orderId=${orderData.id}&status=pending`

      const message = this.formatOrderMessage({
        orderId: orderData.id,
        userName: orderData.user_name,
        userEmail: orderData.user_email,
        productName: orderData.product_name,
        gameId: orderData.game_id,
        amount: orderData.amount,
        price: orderData.price,
        status: orderData.status,
        createdAt: orderData.created_at,
        adminUrl
      })

      // Send to all admin chat IDs
      const results = await Promise.allSettled(
        chatIds.map(chatId => this.sendMessage(message, adminUrl, chatId))
      )

      // Check if at least one notification was sent successfully
      const successCount = results.filter(result => 
        result.status === 'fulfilled' && result.value === true
      ).length

      if (successCount > 0) {
        console.log(`✅ Telegram notifications sent to ${successCount}/${chatIds.length} admins`)
        return true
      } else {
        console.error(`❌ Failed to send notifications to any of ${chatIds.length} admins`)
        return false
      }
    } catch (error) {
      console.error('❌ Failed to send Telegram notification:', error)
      return false
    }
  }

  /**
   * Format order data into a nice Telegram message
   */
  private static formatOrderMessage(data: TelegramMessage): string {
    const statusEmoji = this.getStatusEmoji(data.status)
    const dateFormatted = new Date(data.createdAt).toLocaleString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'Asia/Riyadh'
    })

    return `🔔 *طلب جديد - نيران كارد*

${statusEmoji} *حالة الطلب:* ${this.getStatusText(data.status)}

👤 *تفاصيل العميل:*
• الاسم: ${data.userName}
• البريد: ${data.userEmail}

🎮 *تفاصيل الطلب:*
• المنتج: ${data.productName}
• معرف اللعبة: \`${data.gameId}\`
• الكمية: ${data.amount}
• السعر: $${data.price}

📅 *التاريخ:* ${dateFormatted}
🆔 *رقم الطلب:* \`${data.orderId}\`

🔗 *إدارة الطلب مباشرة:*
\`${data.adminUrl}\`

---
_انسخ الرابط أعلاه وافتحه في المتصفح لإدارة الطلب_`
  }

  /**
   * Send message to specific Telegram chat
   */
  private static async sendMessage(message: string, adminUrl?: string, chatId?: string): Promise<boolean> {
    try {
      const url = `https://api.telegram.org/bot${this.botToken}/sendMessage`
      
      const payload: any = {
        chat_id: chatId,
        text: message,
        parse_mode: 'Markdown',
        disable_web_page_preview: true,
        disable_notification: false
      }

      // Use inline keyboard for HTTPS URLs (production)
      if (adminUrl && adminUrl.startsWith('https://')) {
        payload.reply_markup = {
          inline_keyboard: [[
            {
              text: "🔗 إدارة الطلب مباشرة",
              url: adminUrl
            }
          ]]
        }
      }
      
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      })

      if (!response.ok) {
        const errorData = await response.json()
        console.error(`❌ Telegram API error for chat ${chatId}:`, errorData)
        return false
      }

      return true
    } catch (error) {
      console.error(`❌ Error sending Telegram message to chat ${chatId}:`, error)
      return false
    }
  }

  /**
   * Get emoji for order status
   */
  private static getStatusEmoji(status: string): string {
    switch (status) {
      case 'pending': return '⏳'
      case 'processing': return '🔄'
      case 'completed': return '✅'
      case 'cancelled': return '❌'
      default: return '📦'
    }
  }

  /**
   * Get Arabic status text
   */
  private static getStatusText(status: string): string {
    switch (status) {
      case 'pending': return 'قيد الانتظار'
      case 'processing': return 'قيد المعالجة'
      case 'completed': return 'مكتمل'
      case 'cancelled': return 'ملغي'
      default: return 'غير محدد'
    }
  }
} 