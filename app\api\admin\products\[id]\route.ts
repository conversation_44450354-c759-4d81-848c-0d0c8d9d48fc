import { NextRequest, NextResponse } from 'next/server'
import { SupabaseAdminService } from '../../../../../lib/services/supabase-admin'
import { createSupabaseServerClient } from '../../../../../lib/supabase-server'

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: productId } = await params
    console.log(`🔧 Admin Products API: Starting product update request for ${productId}`)

    // Check admin authentication using session-based approach
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      console.log('❌ Admin Products API: No authenticated user found')
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check if user is admin
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('role, status')
      .eq('id', user.id)
      .single()

    if (profileError || !profile || profile.role !== 'admin' || profile.status !== 'active') {
      console.log('❌ Admin Products API: User is not an active admin:', { 
        role: profile?.role, 
        status: profile?.status 
      })
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    console.log('✅ Admin Products API: Admin authentication successful for user:', user.id)

    const updates = await request.json()

    console.log(`🔧 Admin Products API: Updating product ${productId}`)

    // Update product using admin service
    const updatedProduct = await SupabaseAdminService.updateProduct(productId, updates)

    console.log('✅ Admin Products API: Product updated successfully')

    return NextResponse.json(updatedProduct, {
      headers: {
        'Content-Type': 'application/json',
      },
    })
  } catch (error: any) {
    console.error('❌ Admin Products API: Error updating product:', error)
    return NextResponse.json(
      { error: 'Failed to update product' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: productId } = await params
    console.log(`🔧 Admin Products API: Starting product deletion request for ${productId}`)

    // Check admin authentication using session-based approach
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      console.log('❌ Admin Products API: No authenticated user found')
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check if user is admin
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('role, status')
      .eq('id', user.id)
      .single()

    if (profileError || !profile || profile.role !== 'admin' || profile.status !== 'active') {
      console.log('❌ Admin Products API: User is not an active admin:', { 
        role: profile?.role, 
        status: profile?.status 
      })
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    console.log('✅ Admin Products API: Admin authentication successful for user:', user.id)

    console.log(`🔧 Admin Products API: Deleting product ${productId}`)

    // Delete product using admin service
    await SupabaseAdminService.deleteProduct(productId)

    console.log('✅ Admin Products API: Product deleted successfully')

    return NextResponse.json(
      { message: 'Product deleted successfully' },
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    )
  } catch (error: any) {
    console.error('❌ Admin Products API: Error deleting product:', error)
    return NextResponse.json(
      { error: 'Failed to delete product' },
      { status: 500 }
    )
  }
}
