/**
 * Currency utility functions - USD only
 * Simplified to use only USD currency throughout the application
 */

// Fixed USD currency
const CURRENCY_SYMBOL = '$'
const CURRENCY_CODE = 'USD'

export type SupportedCurrency = 'USD'

/**
 * Get currency symbol (always USD)
 * @returns USD currency symbol
 */
export function getCurrencySymbol(): string {
  return CURRENCY_SYMBOL
}

/**
 * Get currency name (always USD)
 * @returns USD currency name
 */
export function getCurrencyName(): string {
  return 'دولار أمريكي'
}

/**
 * Format amount with USD currency symbol
 * @param amount - Amount to format
 * @param options - Formatting options
 * @returns Formatted USD currency string
 */
export function formatCurrency(
  amount: number,
  options: {
    showSymbol?: boolean
    showCode?: boolean
    decimals?: number
  } = {}
): string {
  const { showSymbol = true, showCode = false, decimals = 2 } = options

  const formattedAmount = amount.toFixed(decimals)
  const symbol = getCurrencySymbol()

  if (showCode) {
    return `${formattedAmount} ${CURRENCY_CODE}`
  }

  if (showSymbol) {
    // USD symbol goes before the number
    return `${symbol}${formattedAmount}`
  }

  return formattedAmount
}

/**
 * Format amount for notifications and messages (USD only)
 * @param amount - Amount to format
 * @returns Formatted USD string for notifications
 */
export function formatCurrencyForNotification(amount: number): string {
  const symbol = getCurrencySymbol()
  const formattedAmount = amount.toFixed(2)

  // USD symbol goes before the number
  return `${symbol}${formattedAmount}`
}

/**
 * Default currency is always USD
 */
export const DEFAULT_CURRENCY = 'USD'
