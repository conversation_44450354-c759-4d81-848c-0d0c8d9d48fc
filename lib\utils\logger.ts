/**
 * Production-Safe Logger
 * Only logs in development environment to improve production performance
 */

class Logger {
  private static isDevelopment = process.env.NODE_ENV === 'development'

  static log(...args: any[]): void {
    if (this.isDevelopment) {
      console.log(...args)
    }
  }

  static warn(...args: any[]): void {
    if (this.isDevelopment) {
      console.warn(...args)
    }
  }

  static error(...args: any[]): void {
    // Always log errors, even in production
    console.error(...args)
  }

  static info(...args: any[]): void {
    if (this.isDevelopment) {
      console.info(...args)
    }
  }

  static debug(...args: any[]): void {
    if (this.isDevelopment) {
      console.debug(...args)
    }
  }

  /**
   * Force log in production (use sparingly)
   */
  static forceLog(...args: any[]): void {
    console.log(...args)
  }
}

export default Logger
