/**
 * Admin Paginated Orders API Route
 * ✅ PERFORMANCE: Server-side pagination with filtering for order management
 */

import { NextRequest, NextResponse } from 'next/server'
import { SupabaseAdminService } from '../../../../../lib/services/supabase-admin'
import { createSupabaseServerClient } from '../../../../../lib/supabase-server'

export async function GET(request: NextRequest) {
  try {
    // 🔒 ADMIN AUTHENTICATION
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check admin privileges
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('role, status')
      .eq('id', user.id)
      .single()

    if (profileError || !profile || profile.role !== 'admin' || profile.status !== 'active') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const pageSize = parseInt(searchParams.get('pageSize') || '20')
    const status = searchParams.get('status') || 'all'
    const search = searchParams.get('search') || ''

    // Validate parameters
    if (page < 1 || pageSize < 1 || pageSize > 100) {
      return NextResponse.json(
        { error: 'Invalid page or pageSize parameters' },
        { status: 400 }
      )
    }

    // ✅ PERFORMANCE: Get paginated results with server-side filtering
    const result = await SupabaseAdminService.getOrdersWithDetailsPaginated(
      status === 'all' ? undefined : status as any,
      page,
      pageSize,
      search
    )

    return NextResponse.json({
      orders: result.orders,
      totalCount: result.totalCount,
      totalPages: result.totalPages,
      currentPage: page,
      pageSize: pageSize,
      hasNextPage: page < result.totalPages,
      hasPreviousPage: page > 1,
      statusFilter: status,
      searchTerm: search
    }, {
      headers: {
        'Cache-Control': 'private, max-age=30', // Cache for 30 seconds (orders change frequently)
        'Content-Type': 'application/json',
      },
    })
  } catch (error: any) {
    // Return empty result instead of error for graceful handling
    return NextResponse.json({
      orders: [],
      totalCount: 0,
      totalPages: 0,
      currentPage: 1,
      pageSize: 20,
      hasNextPage: false,
      hasPreviousPage: false,
      statusFilter: 'all',
      searchTerm: ''
    })
  }
} 