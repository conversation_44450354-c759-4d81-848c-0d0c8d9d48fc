// Date utilities for Supabase (PostgreSQL timestamps)

/**
 * Safely converts Supabase timestamp to formatted date string
 * @param timestamp - PostgreSQL timestamp string, Date object, or null
 * @param locale - Locale for formatting (default: 'ar-SA')
 * @param options - Intl.DateTimeFormatOptions
 * @returns Formatted date string or fallback text
 */
export function formatFirebaseDate(
  timestamp: Date | string | null | undefined,
  locale: string = 'en-US',
  options?: Intl.DateTimeFormatOptions,
  fallback: string = 'غير محدد'
): string {
  try {
    let date: Date

    if (!timestamp) {
      return fallback
    }

    // Handle Date object
    if (timestamp instanceof Date) {
      date = timestamp
    }
    // Handle PostgreSQL timestamp string
    else if (typeof timestamp === 'string') {
      date = new Date(timestamp)
    }
    // Handle invalid cases
    else {
      return fallback
    }

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return fallback
    }

    // Default options for Gregorian calendar
    const defaultOptions: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      calendar: 'gregory',
      ...options
    }

    return date.toLocaleDateString(locale, defaultOptions)
  } catch (error) {
    console.warn('Error formatting date:', error)
    return fallback
  }
}

/**
 * Safely converts Supabase timestamp to formatted date and time string
 * @param timestamp - PostgreSQL timestamp string or Date object
 * @param locale - Locale for formatting (default: 'ar-SA')
 * @param fallback - Fallback text if conversion fails
 * @returns Formatted date and time string
 */
export function formatFirebaseDateTime(
  timestamp: Date | string | null | undefined,
  locale: string = 'en-US',
  fallback: string = 'غير محدد'
): string {
  return formatFirebaseDate(timestamp, locale, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }, fallback)
}

/**
 * Safely converts Supabase timestamp to relative time string
 * @param timestamp - PostgreSQL timestamp string or Date object
 * @param fallback - Fallback text if conversion fails
 * @returns Relative time string (e.g., "منذ 5 دقائق")
 */
export function formatRelativeTime(
  timestamp: Date | string | null | undefined,
  fallback: string = 'غير محدد'
): string {
  try {
    let date: Date

    if (!timestamp) {
      return fallback
    }

    // Handle Date object
    if (timestamp instanceof Date) {
      date = timestamp
    }
    // Handle PostgreSQL timestamp string
    else if (typeof timestamp === 'string') {
      date = new Date(timestamp)
    }
    // Handle invalid cases
    else {
      return fallback
    }

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return fallback
    }

    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) {
      return 'منذ لحظات'
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60)
      return `منذ ${minutes} دقيقة`
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600)
      return `منذ ${hours} ساعة`
    } else if (diffInSeconds < 2592000) {
      const days = Math.floor(diffInSeconds / 86400)
      return `منذ ${days} يوم`
    } else {
      return formatFirebaseDate(timestamp, 'en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        calendar: 'gregory'
      }, fallback)
    }
  } catch (error) {
    console.warn('Error formatting relative time:', error)
    return fallback
  }
}

/**
 * Safely extracts JavaScript Date from Supabase timestamp
 * @param timestamp - PostgreSQL timestamp string or Date object
 * @returns JavaScript Date object or null
 */
export function getDateFromTimestamp(
  timestamp: Date | string | null | undefined
): Date | null {
  try {
    if (!timestamp) {
      return null
    }

    // Handle Date object
    if (timestamp instanceof Date) {
      return timestamp
    }
    // Handle PostgreSQL timestamp string
    else if (typeof timestamp === 'string') {
      const date = new Date(timestamp)
      return isNaN(date.getTime()) ? null : date
    }

    return null
  } catch (error) {
    console.warn('Error extracting date from timestamp:', error)
    return null
  }
}
