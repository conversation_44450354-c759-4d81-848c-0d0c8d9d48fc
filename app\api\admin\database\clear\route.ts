import { NextRequest, NextResponse } from 'next/server'
import { DatabaseManager } from '../../../../../lib/services/database-manager'

// POST - Clear table data
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { tableName, confirm } = body

    if (!tableName) {
      return NextResponse.json(
        { error: 'Table name is required' },
        { status: 400 }
      )
    }

    if (!confirm) {
      return NextResponse.json(
        { error: 'Confirmation required for destructive operation' },
        { status: 400 }
      )
    }

    const deletedRows = await DatabaseManager.clearTable(tableName)

    return NextResponse.json({ 
      success: true, 
      deletedRows,
      message: `Cleared ${deletedRows} rows from ${tableName}` 
    })
  } catch (error: any) {
    // Only log errors in development
    if (process.env.NODE_ENV === 'development') {
      console.error('❌ Admin API: Error clearing table:', error)
    }
    return NextResponse.json(
      { error: error.message || 'Failed to clear table' },
      { status: 500 }
    )
  }
}
