"use client"

interface PlayerIdInputProps {
  playerId: string
  setPlayerId: (id: string) => void
}

export default function PlayerIdInput({ playerId, setPlayerId }: PlayerIdInputProps) {
  return (
    <div className="max-w-md mx-auto">
      <label htmlFor="playerId" className="block text-lg font-semibold text-white mb-3">
        ايدي اللاعب :
      </label>
      <input
        type="text"
        id="playerId"
        value={playerId}
        onChange={(e) => setPlayerId(e.target.value)}
        placeholder="ادخل ايدي اللاعب الخاص بك"
        className="w-full px-4 py-3 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all"
        style={{
          backgroundColor: "#2a2a2a",
          borderColor: "#3a3a3a",
        }}
      />
    </div>
  )
}
