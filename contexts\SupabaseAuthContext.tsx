'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { SupabaseAuthService } from '../lib/services/supabase-auth'
import { User, AuthContextType } from '../types'
import { useToast } from '../hooks/use-toast'
import { NotificationService } from '../lib/services/notification-service'
import SessionManager from '../lib/utils/session-manager'

const SupabaseAuthContext = createContext<AuthContextType | undefined>(undefined)

interface AuthProviderProps {
  children: ReactNode
  initialUser?: User | null
}

export function SupabaseAuthProvider({ children, initialUser = null }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [initialized, setInitialized] = useState(false)
  const [lastAuthAction, setLastAuthAction] = useState<number>(0)
  const { toast } = useToast()
  const sessionManager = SessionManager.getInstance()

  // Only log in development
  if (process.env.NODE_ENV === 'development') {
    console.log(`🔍 SupabaseAuthProvider: Current state - loading=${loading}, user=${user ? `${user.name}(${user.status})` : 'null'}, initialized=${initialized}`)
  }

  // Emergency fallback to prevent infinite loading
  useEffect(() => {
    const emergencyTimeout = setTimeout(() => {
      if (process.env.NODE_ENV === 'development') {
        console.log('🚨 SupabaseAuthProvider: Emergency timeout - forcing loading to false')
      }
      setLoading(false)
      setInitialized(true)
    }, 3000) // 3 second emergency timeout

    return () => clearTimeout(emergencyTimeout)
  }, [])

  // Load user and set up Supabase auth listener
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🚀 SupabaseAuthProvider: useEffect starting...')
    }
    if (typeof window === 'undefined') {
      if (process.env.NODE_ENV === 'development') {
        console.log('⚠️ SupabaseAuthProvider: Window is undefined, skipping')
      }
      return
    }

    // Try to get current session directly from Supabase
    let mounted = true
    let isInitializing = false
    
    const initializeAuth = async () => {
      if (isInitializing) {
        if (process.env.NODE_ENV === 'development') {
          console.log('⚠️ Auth initialization already in progress, skipping')
        }
        return
      }
      
      isInitializing = true
      
      try {
        // First try to get cached user data
        const cachedUser = localStorage.getItem('neran_user')
        const cachedTimestamp = localStorage.getItem('neran_user_timestamp')
        
        if (cachedUser && cachedTimestamp) {
          const age = Date.now() - parseInt(cachedTimestamp)
          if (age < 300000) { // 5 minutes
            if (process.env.NODE_ENV === 'development') {
              console.log('📋 Using cached user data (age: ' + Math.round(age/1000) + 's)')
            }
            const parsedUser = JSON.parse(cachedUser)
            if (mounted) {
              setUser(parsedUser)
              setLoading(false)
              setInitialized(true)
            }
            isInitializing = false
            return
          }
        }
        
        // Get current session with rate limiting protection
        const { data: { session }, error: sessionError } = await SupabaseAuthService.supabase.auth.getSession()
        
        if (!mounted) {
          isInitializing = false
          return
        }
        
        if (sessionError) {
          if (process.env.NODE_ENV === 'development') {
            console.warn('⚠️ Session error:', sessionError)
          }
          // Use cached data if available
          const savedUser = localStorage.getItem('neran_user')
          if (savedUser) {
            const parsedUser = JSON.parse(savedUser)
            setUser(parsedUser)
          } else {
            setUser(null)
          }
        } else if (session?.user) {
          // Get fresh user data with rate limiting
          try {
            const currentUser = await SupabaseAuthService.getCurrentUser()
            if (mounted && currentUser) {
              setUser(currentUser)
              // Save to localStorage for quick access
              localStorage.setItem('neran_user', JSON.stringify(currentUser))
              localStorage.setItem('neran_user_timestamp', Date.now().toString())
            } else {
              // Fallback to cached data if API fails
              const savedUser = localStorage.getItem('neran_user')
              if (savedUser) {
                const parsedUser = JSON.parse(savedUser)
                setUser(parsedUser)
              }
            }
          } catch (userError) {
            if (process.env.NODE_ENV === 'development') {
              console.warn('⚠️ Error getting current user, using cached data:', userError)
            }
            // Fallback to cached data
            const savedUser = localStorage.getItem('neran_user')
            if (savedUser) {
              const parsedUser = JSON.parse(savedUser)
              setUser(parsedUser)
            }
          }
        } else {
          // No session, clear cached data
          setUser(null)
          localStorage.removeItem('neran_user')
          localStorage.removeItem('neran_user_timestamp')
        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.warn('Error initializing auth:', error)
        }
        // Try to load cached data as fallback
        try {
          const savedUser = localStorage.getItem('neran_user')
          if (savedUser && mounted) {
            const parsedUser = JSON.parse(savedUser)
            if (parsedUser.id) {
              setUser(parsedUser)
            }
          }
        } catch (cacheError) {
          if (process.env.NODE_ENV === 'development') {
            console.warn('Error loading cached user:', cacheError)
          }
        }
      } finally {
        if (mounted) {
          setLoading(false)
          setInitialized(true)
        }
        isInitializing = false
      }
    }

    initializeAuth()

    // Set up Supabase auth listener with debouncing
    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 SupabaseAuthProvider: Setting up auth state listener')
    }
    
    let debounceTimer: NodeJS.Timeout | null = null
    let lastUserUpdate = 0
    
    const { data: { subscription } } = SupabaseAuthService.onAuthStateChange(async (user: User | null) => {
      if (!mounted) return
      
      // Prevent too frequent updates (max once per 2 seconds)
      const now = Date.now()
      if (now - lastUserUpdate < 2000) {
        if (process.env.NODE_ENV === 'development') {
          console.log('⏸️ Skipping auth state change - too frequent')
        }
        return
      }
      
      // Debounce rapid state changes
      if (debounceTimer) {
        clearTimeout(debounceTimer)
      }
      
      debounceTimer = setTimeout(async () => {
        lastUserUpdate = Date.now()
        try {
          if (user) {
            if (process.env.NODE_ENV === 'development') {
              console.log('🔄 SupabaseAuthProvider: User detected, processing data')
            }

            setUser(user)

            // Save fresh data to localStorage
            try {
              localStorage.setItem('neran_user', JSON.stringify(user))
              localStorage.setItem('neran_user_timestamp', Date.now().toString())
              if (process.env.NODE_ENV === 'development') {
                console.log('💾 SupabaseAuthProvider: Fresh user data saved to localStorage')
              }
            } catch (error) {
              if (process.env.NODE_ENV === 'development') {
                console.warn('Error saving user to localStorage:', error)
              }
            }
          } else {
            if (process.env.NODE_ENV === 'development') {
              console.log('🚪 SupabaseAuthProvider: No user detected, clearing state')
            }
            setUser(null)

            // Clear localStorage
            try {
              localStorage.removeItem('neran_user')
              localStorage.removeItem('neran_user_timestamp')
            } catch (error) {
              if (process.env.NODE_ENV === 'development') {
                console.warn('Error clearing localStorage:', error)
              }
            }
          }
        } catch (error) {
          if (process.env.NODE_ENV === 'development') {
            console.error('❌ SupabaseAuthProvider: Error in auth state change:', error)
          }
          setUser(null)
        }
      }, 100) // 100ms debounce
    })

    // Reduced fallback timeout to ensure loading is set to false
    const fallbackTimeout = setTimeout(() => {
      if (process.env.NODE_ENV === 'development') {
        console.log('⏰ SupabaseAuthProvider: Fallback timeout - setting loading to false')
      }
      if (mounted) {
        setLoading(false)
        setInitialized(true)
      }
    }, 3000) // Reduced to 3 seconds

    return () => {
      mounted = false
      isInitializing = false
      if (debounceTimer) {
        clearTimeout(debounceTimer)
      }
      if (process.env.NODE_ENV === 'development') {
        console.log('🛑 SupabaseAuthProvider: Cleaning up auth listener')
      }
      clearTimeout(fallbackTimeout)
      try {
        subscription.unsubscribe()
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.warn('⚠️ SupabaseAuthProvider: Error during cleanup:', error)
        }
      }
    }
  }, [])

  // Auto-refresh user data when page becomes visible
  useEffect(() => {
    if (typeof window === 'undefined' || !user?.id) return

    const handleVisibilityChange = async () => {
      if (!document.hidden) {
        console.log('👁️ SupabaseAuthProvider: Page became visible, checking for updates...')
        // Small delay to avoid too frequent requests
        setTimeout(async () => {
          try {
            const freshUserData = await SupabaseAuthService.getCurrentUser()
            if (freshUserData && (
              freshUserData.status !== user.status ||
              freshUserData.role !== user.role ||
              freshUserData.balance !== user.balance
            )) {
              console.log('🎉 SupabaseAuthProvider: User data changed!', {
                oldStatus: user.status,
                newStatus: freshUserData.status,
                oldRole: user.role,
                newRole: freshUserData.role
              })
              setUser(freshUserData)

              // Update localStorage
              try {
                localStorage.setItem('neran_user', JSON.stringify(freshUserData))
                localStorage.setItem('neran_user_timestamp', Date.now().toString())
              } catch (error) {
                console.warn('Error updating localStorage:', error)
              }
            }
          } catch (error) {
            console.warn('Error checking user data on visibility change:', error)
          }
        }, 1000)
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange)
  }, [user?.id, user?.status, user?.role])

  const signIn = async (email: string, password: string): Promise<void> => {
    // Rate limiting: prevent rapid auth requests
    const now = Date.now()
    if (now - lastAuthAction < 2000) { // 2 second cooldown
      throw new Error('يرجى الانتظار قبل المحاولة مرة أخرى')
    }
    setLastAuthAction(now)

    console.log('🔑 SupabaseAuthContext: Starting signin process')
    setLoading(true)

    try {
      const userData = await SupabaseAuthService.signIn(email, password)
      setUser(userData)
      
      // Save to localStorage
      try {
        localStorage.setItem('neran_user', JSON.stringify(userData))
        localStorage.setItem('neran_user_timestamp', Date.now().toString())
        console.log('💾 SupabaseAuthContext: User data saved after signin')
      } catch (error) {
        console.warn('Error saving user after signin:', error)
      }
      
      // Initialize session management
      sessionManager.initialize(() => {
        console.log('⏰ Session expired, signing out user')
        signOut()
        toast({
          title: "⏰ انتهت صلاحية الجلسة",
          description: "تم تسجيل خروجك تلقائياً. يرجى تسجيل الدخول مرة أخرى.",
          variant: "destructive",
        })
      })

      console.log('✅ SupabaseAuthContext: Signin successful')
    } catch (error) {
      console.error('❌ SupabaseAuthContext: Signin failed:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const signUp = async (email: string, password: string, name: string): Promise<void> => {
    console.log('🚀 SupabaseAuthContext: Starting signup process')
    setLoading(true)

    try {
      const userData = await SupabaseAuthService.signUp(email, password, name)
      setUser(userData)
      
      // Save to localStorage
      try {
        localStorage.setItem('neran_user', JSON.stringify(userData))
        localStorage.setItem('neran_user_timestamp', Date.now().toString())
        console.log('💾 SupabaseAuthContext: User data saved after signup')
      } catch (error) {
        console.warn('Error saving user after signup:', error)
      }
      
      console.log('✅ SupabaseAuthContext: Signup successful')
    } catch (error) {
      console.error('❌ SupabaseAuthContext: Signup failed:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const signOut = async (): Promise<void> => {
    setLoading(true)

    try {
      await SupabaseAuthService.signOut()
      setUser(null)

      // Clear session timeout
      sessionManager.clearTimeout()

      // Clear localStorage
      try {
        localStorage.removeItem('neran_user')
        localStorage.removeItem('neran_user_timestamp')
        console.log('🗑️ SupabaseAuthContext: User data cleared after signout')
      } catch (error) {
        console.warn('Error clearing localStorage after signout:', error)
      }
    } catch (error) {
      console.error('Error signing out:', error)
    } finally {
      setLoading(false)
    }
  }

  const updateProfile = async (data: Partial<User>): Promise<void> => {
    if (!user) throw new Error('المستخدم غير مسجل الدخول')

    try {
      console.log('🔧 SupabaseAuthContext: Starting profile update for user:', user.id)
      console.log('🔧 SupabaseAuthContext: Update data:', data)
      
      const updatedUser = await SupabaseAuthService.updateUserProfile(user.id, data)
      
      console.log('✅ SupabaseAuthContext: Profile update successful:', updatedUser)
      setUser(updatedUser)

      // Update localStorage
      try {
        localStorage.setItem('neran_user', JSON.stringify(updatedUser))
        localStorage.setItem('neran_user_timestamp', Date.now().toString())
        console.log('💾 SupabaseAuthContext: Updated user data saved to localStorage')
      } catch (error) {
        console.warn('Error updating localStorage:', error)
      }
    } catch (error) {
      console.error('❌ SupabaseAuthContext: Profile update failed:', error)
      throw error
    }
  }

  const changePassword = async (currentPassword: string, newPassword: string): Promise<void> => {
    if (!user) throw new Error('المستخدم غير مسجل الدخول')
    
    // Rate limiting: prevent rapid password change attempts
    const now = Date.now()
    if (now - lastAuthAction < 3000) { // 3 second cooldown for password changes
      throw new Error('يرجى الانتظار قبل المحاولة مرة أخرى')
    }
    setLastAuthAction(now)

    await SupabaseAuthService.updatePassword(currentPassword, newPassword)
  }

  const sendPasswordResetEmail = async (email: string): Promise<void> => {
    await SupabaseAuthService.resetPassword(email)
  }

  // Refresh user data from Supabase
  const refreshUserData = async (): Promise<User | null> => {
    if (!user?.id) {
      console.warn('⚠️ SupabaseAuthContext: Cannot refresh - no user ID available')
      return null
    }

    try {
      console.log('🔄 SupabaseAuthContext: Manually refreshing user data...')
      const freshUserData = await SupabaseAuthService.getCurrentUser()

      if (freshUserData) {
        const hasChanged = user.status !== freshUserData.status ||
          user.role !== freshUserData.role ||
          user.balance !== freshUserData.balance ||
          user.name !== freshUserData.name ||
          user.email !== freshUserData.email

        if (hasChanged) {
          console.log('✅ SupabaseAuthContext: User data refreshed with changes', {
            oldStatus: user.status,
            newStatus: freshUserData.status,
            oldRole: user.role,
            newRole: freshUserData.role,
            oldBalance: user.balance,
            newBalance: freshUserData.balance
          })

          // Show toast notifications for status changes during manual refresh
          if (user.status !== freshUserData.status) {
            if (freshUserData.status === 'active' && user.status === 'pending') {
              toast({
                title: "🎉 تم تفعيل حسابك!",
                description: "مرحباً بك في نيران كارد. يمكنك الآن استخدام جميع الميزات.",
                variant: "default",
              })
            } else if (freshUserData.status === 'suspended') {
              toast({
                title: "⚠️ تم تعليق حسابك",
                description: "يرجى التواصل مع الدعم الفني لمعرفة السبب.",
                variant: "destructive",
              })
            }
          }

          // Show notifications for role changes during manual refresh
          if (user.role !== freshUserData.role) {
            NotificationService.roleChange(user.role, freshUserData.role, () => {
              window.location.reload()
            })
          }
        } else {
          console.log('✅ SupabaseAuthContext: User data refreshed (no changes)')
        }

        setUser(freshUserData)

        // Update localStorage with fresh data
        try {
          localStorage.setItem('neran_user', JSON.stringify(freshUserData))
          localStorage.setItem('neran_user_timestamp', Date.now().toString())
        } catch (error) {
          console.warn('Error updating localStorage after refresh:', error)
        }

        return freshUserData
      }
      return null
    } catch (error) {
      console.error('❌ SupabaseAuthContext: Error refreshing user data:', error)
      // Don't throw error to avoid breaking the UI
      return null
    }
  }

  // Force refresh with page reload
  const forceRefreshWithReload = async (): Promise<void> => {
    try {
      console.log('🔄 SupabaseAuthContext: Force refreshing with reload...')
      await refreshUserData()
      // Small delay then force reload
      setTimeout(() => {
        window.location.reload()
      }, 500)
    } catch (error) {
      console.error('❌ SupabaseAuthContext: Error in force refresh, reloading anyway:', error)
      window.location.reload()
    }
  }

  const value: AuthContextType = {
    user,
    loading,
    initialized,
    signIn,
    signUp,
    signOut,
    updateProfile,
    changePassword,
    sendPasswordResetEmail,
    refreshUserData,
    forceRefreshWithReload,
  }

  return (
    <SupabaseAuthContext.Provider value={value}>
      {children}
    </SupabaseAuthContext.Provider>
  )
}

export function useSupabaseAuth() {
  const context = useContext(SupabaseAuthContext)
  if (context === undefined) {
    throw new Error('useSupabaseAuth must be used within a SupabaseAuthProvider')
  }
  return context
}
