"use client"

import { useState, useEffect } from "react"
import { TrendingUp, Clock, CheckCircle, AlertCircle, Users, DollarSign, ShoppingCart } from "lucide-react"

export default function AdminOverview() {
  // ALL HOOKS MUST BE CALLED BEFORE ANY CONDITIONAL LOGIC
  const [isClient, setIsClient] = useState(false)
  const [products, setProducts] = useState<any[]>([])
  const [productsLoading, setProductsLoading] = useState(true)
  const [dashboardStats, setDashboardStats] = useState({
    totalUsers: 0,
    totalOrders: 0,
    totalRevenue: 0,
    pendingOrders: 0,
    activeUsers: 0,
    pendingUsers: 0,
    completedOrders: 0,
  })
  const [statsLoading, setStatsLoading] = useState(true)

  // Ensure we're on the client side
  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Only log in development
        if (process.env.NODE_ENV === 'development') {
          console.log('🔧 AdminOverview: Fetching dashboard data via API')
        }

        const [statsResponse, productsResponse] = await Promise.all([
          fetch('/api/admin/dashboard'),
          fetch('/api/admin/products')
        ])

        if (!statsResponse.ok || !productsResponse.ok) {
          throw new Error('Failed to fetch admin data')
        }

        const [stats, productsData] = await Promise.all([
          statsResponse.json(),
          productsResponse.json()
        ])

        // Calculate completed orders from total - pending
        const completedOrders = stats.totalOrders - stats.pendingOrders

        setDashboardStats({
          ...stats,
          completedOrders,
        })
        setProducts(productsData)
      } catch (error) {
        // Only log errors in development
        if (process.env.NODE_ENV === 'development') {
          console.error('❌ AdminOverview: Error fetching dashboard data:', error)
        }

        // Set fallback data so the dashboard doesn't get stuck
        setDashboardStats({
          totalUsers: 0,
          activeUsers: 0,
          pendingUsers: 0,
          totalOrders: 0,
          pendingOrders: 0,
          completedOrders: 0,
          totalRevenue: 0,
        })
        setProducts([])
      } finally {
        setStatsLoading(false)
        setProductsLoading(false)
      }
    }

    fetchData()

    // Refresh stats every 10 minutes for better performance
    const interval = setInterval(fetchData, 600000) // 10 minutes for better performance
    return () => clearInterval(interval)
  }, [])

  // CONDITIONAL RENDERING AFTER ALL HOOKS
  const loading = productsLoading || statsLoading

  if (!isClient) {
    return (
      <div className="space-y-4">
        <div className="bg-gray-800 border border-gray-600 rounded-xl p-6">
          <div className="text-center py-8">
            <div className="text-white">🚀 جاري تحميل الإحصائيات...</div>
            <div className="text-gray-400 text-sm mt-2">تحديث البيانات في الوقت الفعلي</div>
          </div>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="bg-gray-800 border border-gray-600 rounded-xl p-6">
          <div className="text-center py-8">
            <div className="text-white">🚀 جاري تحميل الإحصائيات...</div>
            <div className="text-gray-400 text-sm mt-2">تحديث البيانات في الوقت الفعلي</div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="bg-gray-800 border border-gray-600 rounded-xl p-4 md:p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg md:text-xl font-bold text-white">نظرة عامة على النظام</h2>
          <span className="text-xs text-green-400 bg-green-400/10 px-2 py-1 rounded-full">
            🔄 مباشر
          </span>
        </div>

        {/* Stats Grid - 2 columns on mobile, 4 on desktop */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4">
          {/* Active Users */}
          <div className="bg-gray-900 border border-gray-600 rounded-xl p-4 hover:bg-gray-850 transition-colors">
            <div className="flex flex-col items-center text-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                <Users className="w-6 h-6 text-white" />
              </div>
              <div>
                <div className="text-xl md:text-2xl font-bold text-white">{dashboardStats.activeUsers}</div>
                <div className="text-gray-400 text-xs md:text-sm">المستخدمين النشطين</div>
              </div>
            </div>
          </div>

          {/* Pending Users */}
          <div className="bg-gray-900 border border-gray-600 rounded-xl p-4 hover:bg-gray-850 transition-colors">
            <div className="flex flex-col items-center text-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl flex items-center justify-center">
                <Clock className="w-6 h-6 text-white" />
              </div>
              <div>
                <div className="text-xl md:text-2xl font-bold text-white">{dashboardStats.pendingUsers}</div>
                <div className="text-gray-400 text-xs md:text-sm">طلبات الانتظار</div>
              </div>
            </div>
          </div>

          {/* Total Revenue */}
          <div className="bg-gray-900 border border-gray-600 rounded-xl p-4 hover:bg-gray-850 transition-colors">
            <div className="flex flex-col items-center text-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-white" />
              </div>
              <div>
                <div className="text-xl md:text-2xl font-bold text-white">${dashboardStats.totalRevenue.toFixed(2)}</div>
                <div className="text-gray-400 text-xs md:text-sm">إجمالي المبيعات</div>
                <div className="text-green-400 text-xs">من {dashboardStats.completedOrders} طلب</div>
              </div>
            </div>
          </div>

          {/* Pending Orders */}
          <div className="bg-gray-900 border border-gray-600 rounded-xl p-4 hover:bg-gray-850 transition-colors">
            <div className="flex flex-col items-center text-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center">
                <ShoppingCart className="w-6 h-6 text-white" />
              </div>
              <div>
                <div className="text-xl md:text-2xl font-bold text-white">{dashboardStats.pendingOrders}</div>
                <div className="text-gray-400 text-xs md:text-sm">طلبات معلقة</div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Stats Summary */}
        <div className="mt-6 pt-4 border-t border-gray-700">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-white font-semibold">{dashboardStats.totalUsers}</div>
              <div className="text-gray-400 text-xs">إجمالي المستخدمين</div>
            </div>
            <div>
              <div className="text-white font-semibold">{dashboardStats.totalOrders}</div>
              <div className="text-gray-400 text-xs">إجمالي الطلبات</div>
            </div>
            <div>
              <div className="text-white font-semibold">{products?.length || 0}</div>
              <div className="text-gray-400 text-xs">المنتجات المتاحة</div>
            </div>
            <div>
              <div className="text-white font-semibold">{dashboardStats.completedOrders}</div>
              <div className="text-gray-400 text-xs">طلبات مكتملة</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
