import { NextRequest, NextResponse } from 'next/server'
import { SupabaseAdminService } from '../../../lib/services/supabase-admin'

/**
 * Settings API Route
 * Returns system settings from Supabase database
 */
export async function GET() {
  try {
    console.log('🔄 API: Fetching system settings from Supabase')

    // Fetch system settings from Supabase using SupabaseAdminService
    const settings = await SupabaseAdminService.getSystemSettings()

    console.log('✅ API: Fetched system settings successfully')

    // Add cache headers for better performance but allow quick updates for maintenance mode
    return NextResponse.json(settings, {
      headers: {
        'Cache-Control': 'public, max-age=30, s-maxage=30', // 30 seconds only for quick maintenance mode activation
        'Content-Type': 'application/json',
      },
    })
  } catch (error) {
    console.error('❌ Settings API error:', error)

    // Return default settings if fetch fails
    const defaultSettings = {
      siteName: 'نيران كارد',
      siteDescription: 'منصة شراء العملات الرقمية للألعاب',
      maintenanceMode: false,
      allowRegistrations: true,
      supportWhatsApp: '+966501234567',
      supportEmail: '<EMAIL>',
      language: 'ar',
      autoApproveUsers: true,
      updatedAt: new Date().toISOString(),
    }

    return NextResponse.json(defaultSettings, {
      headers: {
        'Cache-Control': 'public, max-age=60, s-maxage=60', // 1 minute for errors
        'Content-Type': 'application/json',
      },
    })
  }
}

/**
 * Update system settings
 */
export async function PUT(request: NextRequest) {
  try {
    console.log('🔄 API: Updating system settings')

    const settingsData = await request.json()

    // Validate required fields
    if (!settingsData || typeof settingsData !== 'object') {
      return NextResponse.json(
        { error: 'Invalid settings data' },
        { status: 400 }
      )
    }

    console.log('📝 Settings data to update:', settingsData)

    // Update settings using SupabaseAdminService
    await SupabaseAdminService.updateSystemSettings(settingsData)

    console.log('✅ API: System settings updated successfully')

    // Return the updated settings
    const updatedSettings = await SupabaseAdminService.getSystemSettings()

    return NextResponse.json({
      success: true,
      message: 'Settings updated successfully',
      settings: updatedSettings
    }, {
      headers: {
        'Content-Type': 'application/json',
      },
    })
  } catch (error) {
    console.error('❌ Settings update API error:', error)
    return NextResponse.json(
      { error: 'Failed to update settings', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
