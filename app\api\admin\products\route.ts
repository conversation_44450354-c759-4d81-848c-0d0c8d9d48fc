import { NextRequest, NextResponse } from 'next/server'
import { SupabaseAdminService } from '../../../../lib/services/supabase-admin'
import { createSupabaseServerClient } from '../../../../lib/supabase-server'

export async function GET(request: NextRequest) {
  try {
    console.log('🔧 Admin Products API: Starting products fetch request')

    // Check admin authentication using session-based approach
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      console.log('❌ Admin Products API: No authenticated user found')
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check if user is admin
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('role, status')
      .eq('id', user.id)
      .single()

    if (profileError || !profile || profile.role !== 'admin' || profile.status !== 'active') {
      console.log('❌ Admin Products API: User is not an active admin:', { 
        role: profile?.role, 
        status: profile?.status 
      })
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    console.log('✅ Admin Products API: Admin authentication successful for user:', user.id)
    console.log('🔧 Admin Products API: Fetching all products')

    // Get all products using the admin service
    const products = await SupabaseAdminService.getAllProducts()

    console.log(`✅ Admin Products API: Products fetched successfully - count: ${products.length}`)

    // Disable caching for real-time updates
    return NextResponse.json(products, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Content-Type': 'application/json',
      },
    })
  } catch (error: any) {
    console.error('❌ Admin Products API: Error fetching products:', error)

    // Return empty array instead of error to prevent admin dashboard from breaking
    console.log('⚠️ Admin Products API: Returning empty products array due to error')

    return NextResponse.json([], {
      headers: {
        'Cache-Control': 'no-cache', // Don't cache error responses
        'Content-Type': 'application/json',
        'X-Fallback-Data': 'true', // Indicate this is fallback data
      },
    })
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('🔧 Admin Products API: Starting product creation request')

    // Check admin authentication using session-based approach
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      console.log('❌ Admin Products API: No authenticated user found')
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check if user is admin
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('role, status')
      .eq('id', user.id)
      .single()

    if (profileError || !profile || profile.role !== 'admin' || profile.status !== 'active') {
      console.log('❌ Admin Products API: User is not an active admin:', { 
        role: profile?.role, 
        status: profile?.status 
      })
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    console.log('✅ Admin Products API: Admin authentication successful for user:', user.id)

    const productData = await request.json()

    // Validate required fields
    if (!productData.name || !productData.price) {
      return NextResponse.json(
        { error: 'Missing required fields: name, price' },
        { status: 400 }
      )
    }

    console.log('🔧 Admin Products API: Creating new product')

    // Create product using admin service
    const newProduct = await SupabaseAdminService.createProduct(productData)

    console.log('✅ Admin Products API: Product created successfully')

    return NextResponse.json(newProduct, {
      status: 201,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  } catch (error: any) {
    console.error('❌ Admin Products API: Error creating product:', error)
    return NextResponse.json(
      { error: 'Failed to create product' },
      { status: 500 }
    )
  }
}
