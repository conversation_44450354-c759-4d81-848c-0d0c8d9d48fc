// User Types
export interface User {
  id: string
  name: string
  email: string
  balance: number
  status: 'active' | 'pending' | 'suspended'
  role: 'user' | 'admin' | 'distributor'
  createdAt: string
  updatedAt: string
  phoneNumber?: string
  profileImage?: string
  gameId?: string
}

export interface UserProfile extends Omit<User, 'id'> {
  uid: string
}

// Product Types
export interface Product {
  id: string
  name: string
  category: 'gems' | 'membership' | 'pass'
  price: number
  distributorPrice: number
  description: string
  isActive: boolean
  createdAt: string
  updatedAt: string
  imageUrl?: string
  gameType?: string
  sortOrder?: number
  // Offer/Discount fields
  hasOffer?: boolean
  originalPrice?: number
  discountPercentage?: number
  offerStartDate?: string
  offerEndDate?: string
  // Enhanced UI fields
  popular?: boolean
  specialOffer?: boolean
}

// Order Types
export interface Order {
  id: string
  userId: string
  gameId: string
  product: string
  productId: string
  amount: string
  price: number
  status: 'completed' | 'pending' | 'cancelled' | 'processing'
  createdAt: string
  updatedAt: string
  completedAt?: string
  notes?: string
  adminNotes?: string
}

// Price Rule Types
export interface PriceRule {
  id: string
  name: string
  category: 'gems' | 'membership' | 'pass'
  basePrice: number
  discountPercentage: number
  minQuantity: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  validFrom?: string
  validTo?: string
}

// System Settings Types
export interface SystemSettings {
  id: string
  siteName: string
  siteDescription: string
  maintenanceMode: boolean
  allowRegistrations: boolean
  requireEmailVerification: boolean
  autoApproveUsers: boolean
  supportWhatsApp: string
  supportEmail: string
  language: string
  emailNotifications: boolean
  smsNotifications: boolean
  orderNotifications: boolean
  updatedAt: string
}

// Transaction Types
export interface Transaction {
  id: string
  userId: string
  type: 'credit' | 'debit'
  amount: number
  description: string
  orderId?: string
  createdAt: string
  adminId?: string
  orderStatus?: 'pending' | 'processing' | 'completed' | 'cancelled' | null
  orderProductName?: string | null
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Auth Context Types
export interface AuthContextType {
  user: User | null
  loading: boolean
  initialized: boolean
  signIn: (email: string, password: string) => Promise<void>
  signUp: (email: string, password: string, name: string) => Promise<void>
  signOut: () => Promise<void>
  updateProfile: (data: Partial<User>) => Promise<void>
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>
  sendPasswordResetEmail: (email: string) => Promise<void>
  refreshUserData: () => Promise<User | null>
  forceRefreshWithReload: () => Promise<void>
}

// Supabase Table Names
export const TABLES = {
  USERS: 'users',
  PRODUCTS: 'products',
  ORDERS: 'orders',
  PRICE_RULES: 'price_rules',
  SYSTEM_SETTINGS: 'system_settings',
  TRANSACTIONS: 'transactions',
} as const


