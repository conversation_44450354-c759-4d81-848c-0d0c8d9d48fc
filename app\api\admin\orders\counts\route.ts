/**
 * ✅ ORDER COUNTS API: Get counts by status for tab labels
 */

import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '../../../../../lib/supabase-server'
import { supabaseAdmin } from '../../../../../lib/supabase-admin'

export async function GET(request: NextRequest) {
  try {
    // 🔒 ADMIN AUTHENTICATION
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check admin privileges
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('role, status')
      .eq('id', user.id)
      .single()

    if (profileError || !profile || profile.role !== 'admin' || profile.status !== 'active') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    console.log('📊 Fetching order counts by status')

    // Get total count
    const { count: totalCount } = await supabaseAdmin
      .from('orders')
      .select('*', { count: 'exact', head: true })

    // Get pending count
    const { count: pendingCount } = await supabaseAdmin
      .from('orders')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'pending')

    // Get completed count
    const { count: completedCount } = await supabaseAdmin
      .from('orders')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'completed')

    // Get cancelled count
    const { count: cancelledCount } = await supabaseAdmin
      .from('orders')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'cancelled')

    const counts = {
      all: totalCount || 0,
      pending: pendingCount || 0,
      completed: completedCount || 0,
      cancelled: cancelledCount || 0
    }

    console.log('✅ Order counts fetched:', counts)

    return NextResponse.json(counts, {
      headers: {
        'Cache-Control': 'private, max-age=30',
        'Content-Type': 'application/json',
      },
    })
  } catch (error: any) {
    console.error('❌ Order counts API error:', error)
    
    return NextResponse.json({
      all: 0,
      pending: 0,
      completed: 0,
      cancelled: 0
    })
  }
} 