/**
 * SSR Helpers for Server-Side Rendering
 * Provides server-side data fetching functions for Next.js App Router
 */

import { supabaseAdmin } from './supabase-server'
import { SupabaseAdminService } from './services/supabase-admin'
import { Product } from '../types'

// Types for SSR data
export interface SSRProduct {
  id: string
  name: string
  category: 'gems' | 'membership' | 'pass'
  price: number
  distributorPrice: number
  description: string
  isActive: boolean
  createdAt: string
  updatedAt: string
  imageUrl?: string
  gameType?: string
  sortOrder: number
  hasOffer: boolean
  originalPrice?: number
  discountPercentage?: number
  offerStartDate?: string
  offerEndDate?: string
  // Enhanced UI fields
  popular?: boolean
  specialOffer?: boolean
}

export interface SSRSystemSettings {
  siteName: string
  siteDescription: string
  maintenanceMode: boolean
  allowRegistrations: boolean
  supportWhatsApp: string
  supportEmail: string
  language: string
  updatedAt: string
}

export interface SSRInitialData {
  products: SSRProduct[]
  settings: SSRSystemSettings
}

/**
 * Get initial page data for SSR
 * This function runs on the server and can use server-only APIs
 */
export async function getInitialPageData(): Promise<SSRInitialData> {
  try {
    console.log('🔄 SSR: Fetching initial page data')

    // Use the SAME AdminService as the API for consistency
    const [productsResult, settingsResult] = await Promise.allSettled([
      SupabaseAdminService.getAllProducts(),
      SupabaseAdminService.getSystemSettings()
    ])

    // Process products
    let products: SSRProduct[] = []
    if (productsResult.status === 'fulfilled' && productsResult.value) {
      // Filter active products and convert to SSR format
      const activeProducts = productsResult.value.filter(product => product.isActive)
      products = activeProducts.map(mapAppProductToSSR)
      console.log(`✅ SSR: Fetched ${products.length} active products`)
    } else {
      console.warn('⚠️ SSR: Failed to fetch products:', productsResult.status === 'rejected' ? productsResult.reason : 'No data')
      console.warn('⚠️ SSR: Using empty products array')
    }

    // Process settings
    let settings: SSRSystemSettings
    if (settingsResult.status === 'fulfilled' && settingsResult.value) {
      settings = mapAppSettingsToSSR(settingsResult.value)
      console.log('✅ SSR: Fetched system settings')
    } else {
      console.warn('⚠️ SSR: Failed to fetch settings:', settingsResult.status === 'rejected' ? settingsResult.reason : 'No data')
      console.warn('⚠️ SSR: Using default settings')
      settings = getDefaultSettings()
    }

    return {
      products,
      settings
    }
  } catch (error) {
    console.error('❌ SSR: Error fetching initial data:', error)
    
    // Return safe defaults on error
    return {
      products: [],
      settings: getDefaultSettings()
    }
  }
}

/**
 * Map Supabase product to SSR format (legacy)
 */
function mapSupabaseProductToSSR(product: any): SSRProduct {
  return {
    id: product.id,
    name: product.name,
    category: product.category,
    price: product.price,
    distributorPrice: product.distributor_price || product.price,
    description: product.description || '',
    isActive: product.is_active,
    createdAt: product.created_at,
    updatedAt: product.updated_at,
    imageUrl: product.image_url,
    gameType: product.game_type,
    sortOrder: product.sort_order || 0,
    hasOffer: product.has_offer || false,
    originalPrice: product.original_price,
    discountPercentage: product.discount_percentage,
    offerStartDate: product.offer_start_date,
    offerEndDate: product.offer_end_date
  }
}

/**
 * Map App Product (from AdminService) to SSR format
 */
function mapAppProductToSSR(product: Product): SSRProduct {
  return {
    id: product.id,
    name: product.name,
    category: product.category || 'gems',
    price: product.price,
    distributorPrice: product.distributorPrice,
    description: product.description || '',
    isActive: product.isActive,
    createdAt: product.createdAt,
    updatedAt: product.updatedAt,
    imageUrl: product.imageUrl,
    gameType: product.gameType || 'gems',
    sortOrder: product.sortOrder || 0,
    hasOffer: product.hasOffer || false,
    originalPrice: product.originalPrice,
    discountPercentage: product.discountPercentage,
    offerStartDate: product.offerStartDate,
    offerEndDate: product.offerEndDate,
    popular: product.popular || false,
    specialOffer: product.specialOffer || false
  }
}

/**
 * Map Supabase settings to SSR format (legacy)
 */
function mapSupabaseSettingsToSSR(settings: any): SSRSystemSettings {
  return {
    siteName: settings.value?.siteName || 'نيران كارد',
    siteDescription: settings.value?.siteDescription || 'منصة شراء العملات الرقمية للألعاب',
    maintenanceMode: settings.value?.maintenanceMode || false,
    allowRegistrations: settings.value?.allowRegistrations || true,
    supportWhatsApp: settings.value?.supportWhatsApp || '+966501234567',
    supportEmail: settings.value?.supportEmail || '<EMAIL>',
    language: settings.value?.language || 'ar',
    updatedAt: settings.updated_at || new Date().toISOString()
  }
}

/**
 * Map App Settings (from AdminService) to SSR format
 */
function mapAppSettingsToSSR(settings: any): SSRSystemSettings {
  return {
    siteName: settings.siteName || 'نيران كارد',
    siteDescription: settings.siteDescription || 'منصة شراء العملات الرقمية للألعاب',
    maintenanceMode: settings.maintenanceMode || false,
    allowRegistrations: settings.allowRegistrations !== undefined ? settings.allowRegistrations : true,
    supportWhatsApp: settings.supportWhatsApp || '+966501234567',
    supportEmail: settings.supportEmail || '<EMAIL>',
    language: settings.language || 'ar',
    updatedAt: settings.updatedAt || new Date().toISOString()
  }
}

/**
 * Get default settings when database fetch fails
 */
function getDefaultSettings(): SSRSystemSettings {
  return {
    siteName: 'نيران كارد',
    siteDescription: 'منصة شراء العملات الرقمية للألعاب',
    maintenanceMode: false,
    allowRegistrations: true,
    supportWhatsApp: '+966501234567',
    supportEmail: '<EMAIL>',
    language: 'ar',
    updatedAt: new Date().toISOString()
  }
}
