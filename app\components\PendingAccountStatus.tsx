'use client'

import { useState, useEffect } from 'react'
import { Clock, MessageCircle, Copy, AlertCircle } from 'lucide-react'
// Removed Firebase AdminService dependency
import { User as UserType } from '../../types'
import { getDateFromTimestamp } from '../../lib/utils/dateUtils'

interface PendingAccountStatusProps {
  user: UserType
}

export default function PendingAccountStatus({ user }: PendingAccountStatusProps) {
  const [whatsAppNumber, setWhatsAppNumber] = useState<string>("+************")
  const [copied, setCopied] = useState(false)

  useEffect(() => {
    // Fetch WhatsApp number from admin settings
    const fetchWhatsAppNumber = async () => {
      try {
        const response = await fetch('/api/settings')
        if (response.ok) {
          const settings = await response.json()
          if (settings && settings.supportWhatsApp) {
            setWhatsAppNumber(settings.supportWhatsApp)
          }
        }
      } catch (error) {
        console.warn('Could not fetch WhatsApp number from settings, using default:', error)
      }
    }

    fetchWhatsAppNumber()
  }, [])

  const handleWhatsAppContact = () => {
    const message = `🔥 *طلب تأكيد التسجيل - نيران كارد*\n\n` +
      `مرحباً، أريد تأكيد تسجيل حسابي:\n\n` +
      `👤 *معرف المستخدم:* ${user.id}\n` +
      `📧 *الاسم:* ${user.name}\n` +
      `📧 *البريد الإلكتروني:* ${user.email}\n\n` +
      `يرجى تفعيل حسابي لأتمكن من استخدام الموقع.\n\n` +
      `شكراً لكم 🙏`

    const phoneNumber = whatsAppNumber.replace(/\+/g, '')
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`
    window.open(whatsappUrl, '_blank')
  }

  const copyUserId = async () => {
    try {
      await navigator.clipboard.writeText(user.id)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy:', error)
    }
  }

  const getRegistrationDays = () => {
    if (!user.createdAt) return 0
    const createdDate = getDateFromTimestamp(user.createdAt)
    if (!createdDate) return 0
    const daysSince = Math.floor((Date.now() - createdDate.getTime()) / (1000 * 60 * 60 * 24))
    return daysSince
  }

  const getStatusMessage = () => {
    const days = getRegistrationDays()

    if (days < 1) {
      return 'تم استلام طلبك! سيتم المراجعة خلال 24-48 ساعة'
    } else if (days < 3) {
      return 'طلبك قيد المراجعة. نتوقع الرد خلال 24 ساعة'
    } else {
      return 'طلبك قيد المراجعة المتقدمة. يرجى التواصل مع الدعم'
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center px-4 py-8 bg-gray-900">
      <div className="w-full max-w-md">
        {/* Main Card */}
        <div className="bg-gray-800 border border-gray-600 rounded-2xl p-6 shadow-2xl">
          {/* Header */}
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-yellow-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <Clock className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-xl font-bold text-white mb-1">حسابك قيد المراجعة</h1>
            <p className="text-gray-400 text-sm">مرحباً {user.name}</p>
          </div>

          {/* User ID Section */}
          <div className="bg-gray-900 border border-gray-600 rounded-xl p-4 mb-4">
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <label className="block text-xs font-medium text-gray-400 mb-1">معرف المستخدم</label>
                <p className="text-white font-mono text-sm truncate">{user.id}</p>
              </div>
              <button
                onClick={copyUserId}
                className="mr-3 p-2 text-gray-400 hover:text-white transition-colors"
                title="نسخ المعرف"
              >
                {copied ? (
                  <span className="text-green-400 text-xs">تم النسخ!</span>
                ) : (
                  <Copy className="w-4 h-4" />
                )}
              </button>
            </div>
          </div>

          {/* Status Message */}
          <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-xl p-4 mb-4">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-yellow-600 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <Clock className="w-3 h-3 text-white" />
              </div>
              <div className="flex-1">
                <p className="text-yellow-300 text-sm leading-relaxed">
                  {getStatusMessage()}
                </p>
              </div>
            </div>
          </div>

          {/* WhatsApp Contact */}
          <div className="bg-green-500/10 border border-green-500/20 rounded-xl p-4 mb-4">
            <h3 className="text-green-400 font-semibold mb-2 text-sm flex items-center gap-2">
              <MessageCircle className="w-4 h-4" />
              تسريع التفعيل
            </h3>
            <p className="text-green-300 text-xs mb-3 leading-relaxed">
              تواصل معنا عبر واتساب للحصول على تفعيل فوري
            </p>

            <button
              onClick={handleWhatsAppContact}
              className="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg transition-colors font-medium flex items-center justify-center gap-2 text-sm"
            >
              <MessageCircle className="w-4 h-4" />
              تأكيد عبر واتساب
            </button>
          </div>

          {/* Quick Info */}
          <div className="bg-blue-500/10 border border-blue-500/20 rounded-xl p-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-xs text-blue-300">
                <span className="text-blue-400">•</span>
                <span>تأكد من تأكيد بريدك الإلكتروني</span>
              </div>
              <div className="flex items-center gap-2 text-xs text-blue-300">
                <span className="text-blue-400">•</span>
                <span>احتفظ بمعرف المستخدم للمراسلات</span>
              </div>
              <div className="flex items-center gap-2 text-xs text-blue-300">
                <span className="text-blue-400">•</span>
                <span>ستحصل على إشعار فور التفعيل</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
