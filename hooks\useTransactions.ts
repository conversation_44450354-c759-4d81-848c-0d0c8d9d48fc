import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Transaction } from '../types'

/**
 * Hook for fetching user transactions with server-side pagination - NO CACHE for real-time updates
 * ✅ PERFORMANCE: Only fetches transactions for the current page
 */
export function useUserTransactionsPaginated(
  userId: string, 
  page: number = 1, 
  pageSize: number = 10
) {
  return useQuery({
    queryKey: ['transactions', 'user', 'paginated', userId, page, pageSize],
    queryFn: async () => {
      try {
        if (!userId) return { transactions: [], totalCount: 0, totalPages: 0 }
        
        // ✅ SERVER-SIDE PAGINATION: Only fetch current page with cache busting
        const params = new URLSearchParams({
          userId,
          page: page.toString(),
          pageSize: pageSize.toString(),
          // Add cache busting parameter
          _t: Date.now().toString()
        })
        
        const response = await fetch(`/api/transactions/paginated?${params}`, {
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        })
        
        if (!response.ok) {
          throw new Error('Failed to fetch transactions')
        }
        
        const data = await response.json()
        return {
          transactions: data.transactions as Transaction[],
          totalCount: data.totalCount as number,
          totalPages: data.totalPages as number,
          currentPage: page
        }
      } catch (error) {
        console.warn('Error fetching paginated transactions:', error)
        return { transactions: [], totalCount: 0, totalPages: 0, currentPage: page }
      }
    },
    staleTime: 0, // Always consider data stale - fetch immediately
    gcTime: 0, // Don't cache data at all
    enabled: !!userId,
    retry: 1,
    retryDelay: 1000,
    refetchOnWindowFocus: true, // Refetch when window gains focus
    refetchOnMount: true, // Always refetch when component mounts
    refetchInterval: false,
    refetchIntervalInBackground: false,
    refetchOnReconnect: true, // Refetch when reconnecting
  })
}

/**
 * Hook for fetching user transactions (Legacy - for backward compatibility) - NO CACHE for real-time updates
 * ✅ SIMPLE: Real-time data with no caching
 */
export function useUserTransactions(userId: string, limit: number = 10) {
  return useQuery({
    queryKey: ['transactions', 'user', userId, limit],
    queryFn: async () => {
      try {
        if (!userId) return []
        
        // ✅ FIXED: Use API endpoint with cache busting
        const params = new URLSearchParams({
          userId,
          limit: limit.toString(),
          // Add cache busting parameter
          _t: Date.now().toString()
        })
        
        const response = await fetch(`/api/transactions?${params}`, {
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        })
        
        if (!response.ok) {
          throw new Error('Failed to fetch transactions')
        }
        
        const transactions = await response.json()
        return transactions as Transaction[]
      } catch (error) {
        console.warn('Error fetching user transactions, returning empty array:', error)
        return []
      }
    },
    staleTime: 0, // Always consider data stale
    gcTime: 0, // Don't cache data at all
    enabled: !!userId,
    retry: 1, // Only retry once
    retryDelay: 1000, // Wait 1 second before retry
    refetchOnWindowFocus: true, // Refetch when window gains focus
    refetchOnMount: true, // Always refetch when component mounts
    refetchInterval: false, // No auto-polling
    refetchIntervalInBackground: false,
    refetchOnReconnect: true, // Refetch when reconnecting
  })
}

/**
 * Hook for fetching all transactions with server-side pagination (admin only) - NO CACHE for real-time updates
 * ✅ PERFORMANCE: Server-side pagination for admin dashboard
 */
export function useAllTransactionsPaginated(
  page: number = 1, 
  pageSize: number = 20
) {
  return useQuery({
    queryKey: ['transactions', 'admin', 'paginated', page, pageSize],
    queryFn: async () => {
      try {
        // ✅ SERVER-SIDE PAGINATION: Only fetch current page with cache busting
        const params = new URLSearchParams({
          page: page.toString(),
          pageSize: pageSize.toString(),
          // Add cache busting parameter
          _t: Date.now().toString()
        })
        
        const response = await fetch(`/api/admin/transactions/paginated?${params}`, {
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        })
        
        if (!response.ok) {
          throw new Error('Failed to fetch admin transactions')
        }
        
        const data = await response.json()
        return {
          transactions: data.transactions as Transaction[],
          totalCount: data.totalCount as number,
          totalPages: data.totalPages as number,
          currentPage: page
        }
      } catch (error) {
        console.warn('Error fetching paginated admin transactions:', error)
        return { transactions: [], totalCount: 0, totalPages: 0, currentPage: page }
      }
    },
    staleTime: 0, // Always consider data stale
    gcTime: 0, // Don't cache data at all
    retry: 1,
    retryDelay: 1000,
    refetchOnWindowFocus: true, // Refetch when window gains focus
    refetchOnMount: true, // Always refetch when component mounts
    refetchInterval: false,
    refetchIntervalInBackground: false,
    refetchOnReconnect: true, // Refetch when reconnecting
  })
}

/**
 * Hook for fetching all transactions (Legacy - DEPRECATED)
 * ⚠️ WARNING: This fetches large amounts of data and should be replaced with paginated version
 */
export function useAllTransactions(limit: number = 100) {
  return useQuery({
    queryKey: ['transactions', 'all', limit],
    queryFn: async () => {
      try {
        // ⚠️ DEPRECATED: Use useAllTransactionsPaginated instead
        console.warn('🚨 useAllTransactions is deprecated - use useAllTransactionsPaginated for better performance')
        
        // ✅ FIXED: Use admin API endpoint instead of direct service
        const response = await fetch(`/api/admin/transactions?limit=${Math.min(limit, 50)}`) // Cap at 50
        
        if (!response.ok) {
          throw new Error('Failed to fetch all transactions')
        }
        
        const transactions = await response.json()
        return transactions as Transaction[]
      } catch (error) {
        console.warn('Error fetching all transactions, returning empty array:', error)
        return []
      }
    },
    staleTime: 3 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  })
}

/**
 * Hook for creating a purchase transaction
 * ✅ SECURITY NOTE: Purchase transactions are created server-side during order creation
 * This hook is kept for future use but should use API endpoints
 */
export function useCreatePurchaseTransaction() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({
      userId,
      amount,
      description,
      orderId,
    }: {
      userId: string
      amount: number
      description: string
      orderId?: string
    }) => {
      // ✅ FIXED: Use API endpoint instead of direct service
      const response = await fetch('/api/transactions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'purchase',
          userId,
          amount,
          description,
          orderId
        })
      })
      
      if (!response.ok) {
        throw new Error('Failed to create purchase transaction')
      }
      
      return response.json()
    },
    onSuccess: () => {
      // Invalidate and refetch transactions
      queryClient.invalidateQueries({ queryKey: ['transactions'] })
    },
    onError: (error) => {
      console.error('❌ Error creating purchase transaction:', error)
    },
  })
}

/**
 * Hook for creating a deposit transaction with cache invalidation
 * ✅ SECURITY NOTE: This should only be used by admin interfaces
 */
export function useCreateDepositTransaction() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({
      userId,
      amount,
      description,
      adminId,
    }: {
      userId: string
      amount: number
      description: string
      adminId?: string
    }) => {
      // ✅ FIXED: Use admin API endpoint instead of direct service
      const response = await fetch('/api/admin/transactions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'deposit',
          userId,
          amount,
          description,
          adminId
        })
      })
      
      if (!response.ok) {
        throw new Error('Failed to create deposit transaction')
      }
      
      return response.json()
    },
    onSuccess: () => {
      // Invalidate and refetch transactions
      queryClient.invalidateQueries({ queryKey: ['transactions'] })
    },
    onError: (error) => {
      console.error('❌ Error creating deposit transaction:', error)
    },
  })
}

/**
 * Hook for creating a refund transaction with cache invalidation
 * ✅ SECURITY NOTE: This should only be used by admin interfaces
 */
export function useCreateRefundTransaction() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({
      userId,
      amount,
      description,
      orderId,
    }: {
      userId: string
      amount: number
      description: string
      orderId?: string
    }) => {
      // ✅ FIXED: Use admin API endpoint instead of direct service
      const response = await fetch('/api/admin/transactions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'refund',
          userId,
          amount,
          description,
          orderId
        })
      })
      
      if (!response.ok) {
        throw new Error('Failed to create refund transaction')
      }
      
      return response.json()
    },
    onSuccess: () => {
      // Invalidate and refetch transactions
      queryClient.invalidateQueries({ queryKey: ['transactions'] })
    },
    onError: (error) => {
      console.error('❌ Error creating refund transaction:', error)
    },
  })
}

/**
 * Hook for preloading user transactions (useful for wallet page)
 * ✅ SECURITY FIX: Uses API endpoint instead of admin client
 */
export function usePreloadUserTransactions() {
  const queryClient = useQueryClient()

  const preloadUserTransactions = async (userId: string) => {
    await queryClient.prefetchQuery({
      queryKey: ['transactions', 'user', userId, 10],
      queryFn: async () => {
        // ✅ FIXED: Use API endpoint instead of direct service
        const response = await fetch(`/api/transactions?userId=${userId}&limit=10`)
        
        if (!response.ok) {
          throw new Error('Failed to preload transactions')
        }
        
        return response.json()
      },
      staleTime: 30 * 1000, // Updated to match main hook
    })
  }

  return { preloadUserTransactions }
}

/**
 * Hook for getting formatted transactions for display
 * ✅ SECURITY FIX: Uses API endpoint instead of admin client
 */
export function useFormattedUserTransactions(userId: string, limit: number = 10) {
  return useQuery({
    queryKey: ['transactions', 'formatted', userId, limit],
    queryFn: async () => {
      try {
        if (!userId) return []
        
        // ✅ FIXED: Use API endpoint instead of direct service
        const response = await fetch(`/api/transactions?userId=${userId}&limit=${limit}`)
        
        if (!response.ok) {
          throw new Error('Failed to fetch transactions')
        }
        
        const transactions = await response.json() as Transaction[]

        // Simple inline formatting since formatTransactionForDisplay doesn't exist
        return transactions.map((transaction: Transaction) => ({
          ...transaction,
          formattedAmount: `$${transaction.amount.toFixed(2)}`,
          formattedDate: new Date(transaction.createdAt).toLocaleDateString('ar-SA'),
          typeLabel: transaction.type === 'credit' ? 'إيداع' : 'خصم'
        }))
      } catch (error) {
        console.warn('Error fetching formatted user transactions, returning empty array:', error)
        return []
      }
    },
    staleTime: 30 * 1000, // Updated to match main hook for consistency
    gcTime: 5 * 60 * 1000, // Updated to match main hook
    enabled: !!userId,
    refetchOnWindowFocus: true, // Updated to match main hook
    refetchOnMount: true, // Updated to match main hook
    retry: 1, // Only retry once
    retryDelay: 1000, // Wait 1 second before retry
  })
}
