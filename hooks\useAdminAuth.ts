// Solution 3: Custom Hook Approach
// This encapsulates the auth logic and provides a clean interface

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useSupabaseAuth } from '../contexts/SupabaseAuthContext'
import type { User, Order } from '../types'

export type AdminAuthState = 
  | { status: 'loading' }
  | { status: 'unauthenticated' }
  | { status: 'unauthorized' }
  | { status: 'authorized'; user: any }

/**
 * ✅ PERFORMANCE: Get paginated admin users with server-side search and filtering - NO CACHE for real-time updates
 */
export function useAdminUsersPaginated(
  page: number = 1,
  pageSize: number = 20,
  search: string = '',
  status: string = 'all'
) {
  return useQuery({
    queryKey: ['admin', 'users', 'paginated', page, pageSize, search, status],
    queryFn: async () => {
      console.log(`🔄 Fetching paginated admin users - page: ${page}, search: "${search}", status: ${status}`)
      
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
        search: search,
        status: status,
        // Add cache busting parameter
        _t: Date.now().toString()
      })

      const response = await fetch(`/api/admin/users/paginated?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        },
      })

      if (!response.ok) {
        console.error('❌ Failed to fetch paginated admin users:', response.status)
        throw new Error('Failed to fetch paginated admin users')
      }

      const result = await response.json()
      console.log(`✅ Fetched paginated admin users: ${result.users.length} users`)
      return result
    },
    staleTime: 0, // Always consider data stale - fetch immediately
    gcTime: 0, // Don't cache data at all (garbage collection time)
    refetchInterval: false, // Manual refresh only
    refetchOnMount: true, // Always refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when window gains focus
    refetchOnReconnect: true, // Refetch when reconnecting
    placeholderData: {
      users: [],
      totalCount: 0,
      totalPages: 0,
      currentPage: page,
      pageSize: pageSize,
      hasNextPage: false,
      hasPreviousPage: false,
      searchTerm: search,
      statusFilter: status
    }
  })
}

/**
 * ✅ Get order counts by status for tab labels - NO CACHE for real-time updates
 */
export function useOrderCounts() {
  return useQuery({
    queryKey: ['admin', 'orders', 'counts'],
    queryFn: async () => {
      console.log('🔄 Fetching order counts by status')
      
      const params = new URLSearchParams({
        // Add cache busting parameter
        _t: Date.now().toString()
      })
      
      const response = await fetch(`/api/admin/orders/counts?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        },
      })

      if (!response.ok) {
        console.error('❌ Failed to fetch order counts:', response.status)
        throw new Error('Failed to fetch order counts')
      }

      const result = await response.json()
      console.log('✅ Fetched order counts:', result)
      return result
    },
    staleTime: 0, // Always consider data stale
    gcTime: 0, // Don't cache data at all
    refetchInterval: false, // Manual refresh only
    refetchOnMount: true, // Always refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when window gains focus
    placeholderData: {
      all: 0,
      pending: 0,
      completed: 0,
      cancelled: 0
    }
  })
}

/**
 * ✅ PERFORMANCE: Get paginated admin orders with server-side filtering - NO CACHE for real-time updates
 */
export function useAdminOrdersPaginated(
  page: number = 1,
  pageSize: number = 20,
  status: string = 'all',
  search: string = ''
) {
  return useQuery({
    queryKey: ['admin', 'orders', 'paginated', page, pageSize, status, search],
    queryFn: async () => {
      console.log(`🔄 Fetching paginated admin orders - page: ${page}, status: ${status}, search: "${search}"`)
      
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
        status: status,
        search: search,
        // Add cache busting parameter
        _t: Date.now().toString()
      })

      const response = await fetch(`/api/admin/orders/paginated?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        },
      })

      if (!response.ok) {
        console.error('❌ Failed to fetch paginated admin orders:', response.status)
        throw new Error('Failed to fetch paginated admin orders')
      }

      const result = await response.json()
      console.log(`✅ Fetched paginated admin orders: ${result.orders.length} orders`)
      return result
    },
    staleTime: 0, // Always consider data stale - fetch immediately
    gcTime: 0, // Don't cache data at all (garbage collection time)
    refetchInterval: false, // Manual refresh only
    refetchOnMount: true, // Always refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when window gains focus
    refetchOnReconnect: true, // Refetch when reconnecting
    placeholderData: {
      orders: [],
      totalCount: 0,
      totalPages: 0,
      currentPage: page,
      pageSize: pageSize,
      hasNextPage: false,
      hasPreviousPage: false,
      statusFilter: status,
      searchTerm: search
    }
  })
}

/**
 * Existing hook - keeping for backward compatibility
 */
export function useAdminAuth(): AdminAuthState {
  const { user, loading } = useSupabaseAuth()

  if (loading) {
    return { status: 'loading' }
  }

  if (!user) {
    return { status: 'unauthenticated' }
  }

  if (user.role !== 'admin') {
    return { status: 'unauthorized' }
  }

  return { status: 'authorized', user }
}

// Usage in component:
/*
function AdminDashboard() {
  const authState = useAdminAuth()
  const [activeSection, setActiveSection] = useState<AdminSection>("overview")
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false)
  const [showQuickActions, setShowQuickActions] = useState(false)

  // All hooks are called consistently
  useEffect(() => {
    // sidebar logic
  }, [isMobileSidebarOpen])

  // Conditional rendering based on auth state
  switch (authState.status) {
    case 'loading':
      return <LoadingState />
    case 'unauthenticated':
      return <UnauthenticatedState />
    case 'unauthorized':
      return <UnauthorizedState />
    case 'authorized':
      return <AdminDashboardContent user={authState.user} />
  }
}
*/
