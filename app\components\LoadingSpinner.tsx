'use client'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg'
  text?: string
  className?: string
}

/**
 * Optimized Loading Spinner Component
 * Provides consistent loading states across the application
 */
export default function LoadingSpinner({ 
  size = 'md', 
  text = 'جاري التحميل...', 
  className = '' 
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12'
  }

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  }

  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      <div 
        className={`animate-spin rounded-full border-2 border-gray-600 border-t-red-600 ${sizeClasses[size]}`}
        role="status"
        aria-label="Loading"
      />
      {text && (
        <p className={`mt-2 text-gray-400 ${textSizeClasses[size]}`}>
          {text}
        </p>
      )}
    </div>
  )
}

/**
 * Page Loading Component
 * For full-page loading states
 */
export function PageLoading({ text = 'جاري تحميل الصفحة...' }: { text?: string }) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-900">
      <LoadingSpinner size="lg" text={text} />
    </div>
  )
}

/**
 * Section Loading Component
 * For section-specific loading states
 */
export function SectionLoading({ text = 'جاري التحميل...' }: { text?: string }) {
  return (
    <div className="flex items-center justify-center py-12">
      <LoadingSpinner size="md" text={text} />
    </div>
  )
}

/**
 * Button Loading Component
 * For button loading states
 */
export function ButtonLoading() {
  return (
    <LoadingSpinner size="sm" text="" className="inline-flex" />
  )
}

/**
 * Skeleton Loading Components
 * For better perceived performance
 */
export function ProductCardSkeleton() {
  return (
    <div className="bg-gray-800 rounded-lg p-4 animate-pulse">
      <div className="h-32 bg-gray-700 rounded mb-4"></div>
      <div className="h-4 bg-gray-700 rounded w-3/4 mb-2"></div>
      <div className="h-4 bg-gray-700 rounded w-1/2 mb-2"></div>
      <div className="h-8 bg-gray-700 rounded w-full"></div>
    </div>
  )
}

export function UserCardSkeleton() {
  return (
    <div className="bg-gray-800 rounded-lg p-4 animate-pulse">
      <div className="flex items-center space-x-4 space-x-reverse">
        <div className="h-12 w-12 bg-gray-700 rounded-full"></div>
        <div className="flex-1">
          <div className="h-4 bg-gray-700 rounded w-1/2 mb-2"></div>
          <div className="h-3 bg-gray-700 rounded w-1/3"></div>
        </div>
        <div className="h-8 w-20 bg-gray-700 rounded"></div>
      </div>
    </div>
  )
}

export function OrderCardSkeleton() {
  return (
    <div className="bg-gray-800 rounded-lg p-4 animate-pulse">
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1">
          <div className="h-4 bg-gray-700 rounded w-1/2 mb-2"></div>
          <div className="h-3 bg-gray-700 rounded w-1/3"></div>
        </div>
        <div className="h-6 w-16 bg-gray-700 rounded"></div>
      </div>
      <div className="h-3 bg-gray-700 rounded w-1/4"></div>
    </div>
  )
}

export function StatCardSkeleton() {
  return (
    <div className="bg-gray-800 rounded-lg p-6 animate-pulse">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="h-4 bg-gray-700 rounded w-1/2 mb-2"></div>
          <div className="h-8 bg-gray-700 rounded w-3/4"></div>
        </div>
        <div className="h-12 w-12 bg-gray-700 rounded"></div>
      </div>
    </div>
  )
}
