"use client"

import { create<PERSON>ontext, useContext, useState, type ReactNode } from "react"

interface User {
  id: string
  name: string
  email: string
  balance: number
  status: "active" | "pending" | "suspended"
  role: "user" | "admin"
  createdAt: string
}

interface Order {
  id: string
  userId: string
  gameId: string
  product: string
  amount: string
  price: number
  status: "completed" | "pending" | "cancelled"
  date: string
}

interface UserContextType {
  currentUser: User | null
  users: User[]
  orders: Order[]
  pendingRegistrations: User[]
  setCurrentUser: (user: User | null) => void
  updateUserBalance: (userId: string, newBalance: number) => void
  approveUser: (userId: string) => void
  rejectUser: (userId: string) => void
  addOrder: (order: Omit<Order, "id" | "date">) => void
  updateOrderStatus: (orderId: string, status: Order["status"]) => void
}

const UserContext = createContext<UserContextType | undefined>(undefined)

export function UserProvider({ children }: { children: ReactNode }) {
  // Mock current user
  const [currentUser, setCurrentUser] = useState<User | null>({
    id: "USR1234567",
    name: "أحمد محمد",
    email: "<EMAIL>",
    balance: 127.5,
    status: "active",
    role: "user",
    createdAt: "2024-01-15",
  })

  // Mock users data
  const [users, setUsers] = useState<User[]>([
    {
      id: "USR1234567",
      name: "أحمد محمد",
      email: "<EMAIL>",
      balance: 127.5,
      status: "active",
      role: "user",
      createdAt: "2024-01-15",
    },
    {
      id: "USR2345678",
      name: "فاطمة علي",
      email: "<EMAIL>",
      balance: 45.75,
      status: "active",
      role: "user",
      createdAt: "2024-01-20",
    },
    {
      id: "USR3456789",
      name: "محمد سالم",
      email: "<EMAIL>",
      balance: 0.0,
      status: "suspended",
      role: "user",
      createdAt: "2024-01-25",
    },
  ])

  // Mock pending registrations
  const [pendingRegistrations, setPendingRegistrations] = useState<User[]>([
    {
      id: "USR4567890",
      name: "نورا أحمد",
      email: "<EMAIL>",
      balance: 0.0,
      status: "pending",
      role: "user",
      createdAt: "2024-02-01",
    },
    {
      id: "USR5678901",
      name: "خالد عبدالله",
      email: "<EMAIL>",
      balance: 0.0,
      status: "pending",
      role: "user",
      createdAt: "2024-02-02",
    },
  ])

  // Mock orders data
  const [orders, setOrders] = useState<Order[]>([
    {
      id: "ORD12345",
      userId: "USR1234567",
      gameId: "123456789",
      product: "💎 571 جوهرة",
      amount: "571 جوهرة",
      price: 11.25,
      status: "completed",
      date: "2024-02-05",
    },
    {
      id: "ORD12346",
      userId: "USR2345678",
      gameId: "987654321",
      product: "عضوية شهرية",
      amount: "عضوية شهرية",
      price: 16.0,
      status: "pending",
      date: "2024-02-04",
    },
  ])

  const updateUserBalance = (userId: string, newBalance: number) => {
    setUsers((prev) => prev.map((user) => (user.id === userId ? { ...user, balance: newBalance } : user)))
    if (currentUser?.id === userId) {
      setCurrentUser((prev) => (prev ? { ...prev, balance: newBalance } : null))
    }
  }

  const approveUser = (userId: string) => {
    const userToApprove = pendingRegistrations.find((user) => user.id === userId)
    if (userToApprove) {
      const approvedUser = { ...userToApprove, status: "active" as const }
      setUsers((prev) => [...prev, approvedUser])
      setPendingRegistrations((prev) => prev.filter((user) => user.id !== userId))
    }
  }

  const rejectUser = (userId: string) => {
    setPendingRegistrations((prev) => prev.filter((user) => user.id !== userId))
  }

  const addOrder = (orderData: Omit<Order, "id" | "date">) => {
    const newOrder: Order = {
      ...orderData,
      id: `ORD${Math.random().toString().substr(2, 5)}`,
      date: new Date().toISOString().split("T")[0],
    }
    setOrders((prev) => [newOrder, ...prev])
  }

  const updateOrderStatus = (orderId: string, status: Order["status"]) => {
    setOrders((prev) => prev.map((order) => (order.id === orderId ? { ...order, status } : order)))
  }

  return (
    <UserContext.Provider
      value={{
        currentUser,
        users,
        orders,
        pendingRegistrations,
        setCurrentUser,
        updateUserBalance,
        approveUser,
        rejectUser,
        addOrder,
        updateOrderStatus,
      }}
    >
      {children}
    </UserContext.Provider>
  )
}

export function useUser() {
  const context = useContext(UserContext)
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider")
  }
  return context
}
