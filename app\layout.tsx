import type React from "react"
import type { Metadata } from "next"
import { Cairo } from "next/font/google"
import "./globals.css"
import Navbar from "./components/Navbar"
import Footer from "./components/Footer"
import { SupabaseAuthProvider } from "../contexts/SupabaseAuthContext"
import QueryProvider from "../contexts/QueryProvider"
import ClientComponents from "./components/ClientComponents"

const cairo = Cairo({
  subsets: ["arabic", "latin"],
  display: "swap",
  preload: true,
  fallback: ['system-ui', 'arial'],
})

export const metadata: Metadata = {
  title: "نيران كارد - شحن بطاقات الألعاب والتطبيقات | Neran Card",
  description:
    "نيران كارد - أفضل موقع لشحن بطاقات الألعاب والتطبيقات. شحن فوري وآمن لجميع الألعاب مثل PUBG, Free Fire, Fortnite وأكثر. أسعار منافسة وخدمة عملاء ممتازة.",
  keywords: [
    "نيران كارد",
    "neran card",
    "nerancard",
    "شحن ألعاب",
    "بطاقات ألعاب",
    "شحن PUBG",
    "شحن Free Fire",
    "شحن Fortnite",
    "جواهر PUBG",
    "عضويات ألعاب",
    "شحن فوري",
    "بطاقات تطبيقات",
    "شحن آمن",
    "gaming cards",
    "game top up",
    "mobile gaming",
    "gaming credits",
    "digital cards",
    "instant delivery",
    "gaming store",
    "online gaming",
    "mobile games",
    "gaming platform",
  ].join(", "),
  authors: [{ name: "Neran Card", url: "https://nerancard.com" }],
  creator: "Neran Card",
  publisher: "Neran Card",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "ar_SA",
    alternateLocale: ["en_US"],
    url: "https://nerancard.com",
    siteName: "نيران كارد - Neran Card",
    title: "نيران كارد - شحن بطاقات الألعاب والتطبيقات | Neran Card",
    description:
      "نيران كارد - أفضل موقع لشحن بطاقات الألعاب والتطبيقات. شحن فوري وآمن لجميع الألعاب مثل PUBG, Free Fire, Fortnite وأكثر.",
    images: [
      {
        url: "https://nerancard.com/images/neran-logo.png",
        width: 1200,
        height: 630,
        alt: "نيران كارد - شحن بطاقات الألعاب",
        type: "image/png",
      },
      {
        url: "https://nerancard.com/images/neran-logo.png",
        width: 1200,
        height: 1200,
        alt: "نيران كارد - شحن بطاقات الألعاب",
        type: "image/png",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    site: "@nerancard",
    creator: "@nerancard",
    title: "نيران كارد - شحن بطاقات الألعاب والتطبيقات",
    description: "نيران كارد - أفضل موقع لشحن بطاقات الألعاب والتطبيقات. شحن فوري وآمن لجميع الألعاب.",
    images: ["https://nerancard.com/images/neran-logo.png"],
  },
  verification: {
    google: "your-google-verification-code",
    yandex: "your-yandex-verification-code",
    yahoo: "your-yahoo-verification-code",
  },
  alternates: {
    canonical: "https://nerancard.com",
    languages: {
      "ar-SA": "https://nerancard.com",
      "en-US": "https://nerancard.com/en",
    },
  },
  category: "Gaming",
  classification: "Gaming Cards and Digital Services",
  other: {
    "apple-mobile-web-app-title": "نيران كارد",
    "application-name": "Neran Card",
    "msapplication-TileColor": "#8B2635",
    "theme-color": "#8B2635",
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  },
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl" className="dark">
      <head>
        {/* Favicon and Icons */}
        <link rel="icon" type="image/png" sizes="32x32" href="/images/neran-logo.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/images/neran-logo.png" />
        <link rel="apple-touch-icon" href="/images/neran-logo.png" />
        <link rel="shortcut icon" href="/images/neran-logo.png" />

        {/* PWA Manifest */}
        <link rel="manifest" href="/manifest.json" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        <meta name="apple-mobile-web-app-title" content="نيران كارد" />

        {/* Performance optimizations */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="dns-prefetch" href="https://supabase.co" />

        {/* Prefetch critical pages */}
        <link rel="prefetch" href="/auth" />
        <link rel="prefetch" href="/profile" />
        <link rel="prefetch" href="/wallet" />

        {/* SEO and language */}
        <link rel="canonical" href="https://nerancard.com" />
        <link rel="alternate" hrefLang="ar" href="https://nerancard.com" />
        <link rel="alternate" hrefLang="en" href="https://nerancard.com/en" />
        <link rel="alternate" hrefLang="x-default" href="https://nerancard.com" />

        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              name: "نيران كارد",
              alternateName: "Neran Card",
              url: "https://nerancard.com",
              logo: "https://nerancard.com/images/neran-logo.png",
              description: "أفضل موقع لشحن بطاقات الألعاب والتطبيقات في الشرق الأوسط",
              sameAs: [
                "https://twitter.com/nerancard",
                "https://facebook.com/nerancard",
                "https://instagram.com/nerancard",
              ],
              contactPoint: {
                "@type": "ContactPoint",
                telephone: "+966501234567",
                contactType: "customer service",
                availableLanguage: ["Arabic", "English"],
              },
              areaServed: {
                "@type": "Country",
                name: "Saudi Arabia",
              },
              serviceType: "Gaming Cards and Digital Services",
            }),
          }}
        />

        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebSite",
              name: "نيران كارد",
              url: "https://nerancard.com",
              potentialAction: {
                "@type": "SearchAction",
                target: "https://nerancard.com/search?q={search_term_string}",
                "query-input": "required name=search_term_string",
              },
            }),
          }}
        />
      </head>
      <body className={cairo.className}>
        <div className="min-h-screen flex flex-col">
          <QueryProvider>
            <SupabaseAuthProvider>
              <Navbar />
              <main className="pt-16 flex-1">{children}</main>
              <Footer />
              <ClientComponents />
            </SupabaseAuthProvider>
          </QueryProvider>
        </div>
      </body>
    </html>
  )
}
