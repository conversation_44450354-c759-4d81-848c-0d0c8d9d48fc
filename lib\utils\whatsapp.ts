/**
 * WhatsApp utility functions with enhanced iOS/Android support
 */

export interface WhatsAppContactOptions {
  phoneNumber: string
  message: string
  fallbackAlert?: boolean
}

/**
 * Open WhatsApp with enhanced compatibility for iOS and Android
 * Tries multiple URL formats and provides fallbacks
 */
export function openWhatsAppContact({ phoneNumber, message, fallbackAlert = true }: WhatsAppContactOptions): void {
  const cleanPhoneNumber = phoneNumber.replace(/\+/g, '')
  const encodedMessage = encodeURIComponent(message)
  
  // Multiple WhatsApp URL formats for better compatibility
  const whatsappUrls = [
    `https://wa.me/${cleanPhoneNumber}?text=${encodedMessage}`, // Standard web format
    `https://api.whatsapp.com/send?phone=${cleanPhoneNumber}&text=${encodedMessage}`, // API format
    `whatsapp://send?phone=${cleanPhoneNumber}&text=${encodedMessage}` // Deep link for mobile apps
  ]
  
  let urlIndex = 0
  
  const tryOpenWhatsApp = () => {
    if (urlIndex >= whatsappUrls.length) {
      // All URLs failed, try fallback
      if (fallbackAlert) {
        handleFallback(phoneNumber, message)
      }
      return
    }
    
    const url = whatsappUrls[urlIndex]
    console.log(`🔗 Trying WhatsApp URL ${urlIndex + 1}:`, url)
    
    try {
      const newWindow = window.open(url, '_blank', 'noopener,noreferrer')
      
      // Check if window opened successfully after a short delay
      setTimeout(() => {
        if (!newWindow || newWindow.closed || newWindow.location.href === 'about:blank') {
          console.log(`❌ WhatsApp URL ${urlIndex + 1} failed, trying next...`)
          urlIndex++
          tryOpenWhatsApp()
        } else {
          console.log(`✅ WhatsApp URL ${urlIndex + 1} opened successfully`)
        }
      }, 1500)
      
    } catch (error) {
      console.log(`❌ Error opening WhatsApp URL ${urlIndex + 1}:`, error)
      urlIndex++
      tryOpenWhatsApp()
    }
  }
  
  tryOpenWhatsApp()
}

/**
 * Fallback when WhatsApp URLs fail
 */
function handleFallback(phoneNumber: string, message: string): void {
  // Try to copy message to clipboard
  if (navigator.clipboard && navigator.clipboard.writeText) {
    navigator.clipboard.writeText(message).then(() => {
      alert(`📋 تم نسخ الرسالة إلى الحافظة!\n\nيرجى فتح واتساب والتواصل مع:\n${phoneNumber}\n\nثم لصق الرسالة المنسوخة.`)
    }).catch(() => {
      showManualInstructions(phoneNumber, message)
    })
  } else {
    showManualInstructions(phoneNumber, message)
  }
}

/**
 * Show manual instructions when all else fails
 */
function showManualInstructions(phoneNumber: string, message: string): void {
  const instructions = `📱 للتواصل مع الدعم الفني:\n\n` +
    `1️⃣ افتح تطبيق واتساب\n` +
    `2️⃣ ابحث عن الرقم: ${phoneNumber}\n` +
    `3️⃣ أرسل الرسالة التالية:\n\n` +
    `"${message}"\n\n` +
    `أو انسخ الرسالة من هذا التنبيه.`
  
  alert(instructions)
}

/**
 * Generate pre-filled WhatsApp message for different scenarios
 */
export function generateWhatsAppMessage(type: 'banned' | 'pending' | 'support' | 'order', data: any): string {
  const baseInfo = data.user ? `👤 *معرف المستخدم:* ${data.user.id}\n📧 *الاسم:* ${data.user.name}\n📧 *البريد الإلكتروني:* ${data.user.email}\n\n` : ''
  
  switch (type) {
    case 'banned':
      return `🚫 *استفسار حول حظر الحساب - نيران كارد*\n\n` +
        `مرحباً، أريد الاستفسار عن حظر حسابي:\n\n` +
        baseInfo +
        `تم حظر حسابي ولا أعرف السبب. يرجى توضيح السبب وإمكانية إعادة التفعيل.\n\n` +
        `شكراً لكم 🙏`
    
    case 'pending':
      return `🔥 *طلب تأكيد التسجيل - نيران كارد*\n\n` +
        `مرحباً، أريد تأكيد تسجيل حسابي:\n\n` +
        baseInfo +
        `يرجى تفعيل حسابي لأتمكن من استخدام الموقع.\n\n` +
        `شكراً لكم 🙏`
    
    case 'support':
      return `💬 *طلب دعم فني - نيران كارد*\n\n` +
        `مرحباً، أحتاج مساعدة في:\n\n` +
        baseInfo +
        `${data.issue || 'يرجى وصف المشكلة هنا...'}\n\n` +
        `شكراً لكم 🙏`
    
    case 'order':
      return `🛍️ *استفسار حول الطلب - نيران كارد*\n\n` +
        `مرحباً، لدي استفسار حول طلبي:\n\n` +
        baseInfo +
        `🆔 *رقم الطلب:* ${data.orderId || 'غير محدد'}\n` +
        `💰 *المبلغ:* ${data.amount || 'غير محدد'}\n` +
        `📅 *تاريخ الطلب:* ${data.date || 'غير محدد'}\n\n` +
        `${data.issue || 'يرجى وصف المشكلة أو الاستفسار هنا...'}\n\n` +
        `شكراً لكم 🙏`
    
    default:
      return `💬 *رسالة من نيران كارد*\n\n` +
        `مرحباً،\n\n` +
        baseInfo +
        `شكراً لكم 🙏`
  }
}

/**
 * Quick function to contact support with pre-filled message
 */
export function contactSupport(phoneNumber: string, user?: any, issue?: string): void {
  const message = generateWhatsAppMessage('support', { user, issue })
  openWhatsAppContact({ phoneNumber, message })
}
