/**
 * Supabase Admin Client Configuration
 * This file is ONLY for server-side admin operations (API routes)
 * DO NOT import this in client-side code or components
 */

import { createClient } from '@supabase/supabase-js'

// Server-side Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

if (!supabaseUrl) {
  console.error('❌ NEXT_PUBLIC_SUPABASE_URL is missing')
  throw new Error('NEXT_PUBLIC_SUPABASE_URL is required')
}

if (!supabaseServiceKey) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY is missing')
  throw new Error('SUPABASE_SERVICE_ROLE_KEY is required for admin operations')
}

// Log configuration in debug mode
if (process.env.NEXT_PUBLIC_ENABLE_DEBUG === 'true') {
  console.log('🔧 Supabase Admin Config:', {
    url: supabaseUrl,
    serviceKeyLength: supabaseServiceKey.length,
    environment: process.env.NODE_ENV
  })
}

// Admin client for server-side operations (API routes, admin functions)
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  },
  realtime: {
    // disabled: true // Removed - not supported in this version
  },
  global: {
    headers: {
      'User-Agent': 'neran-admin-client',
      'X-Client-Info': 'supabase-js-admin'
    }
  }
})

// Factory function for creating admin clients
export const createSupabaseAdminClient = () => {
  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    },
    realtime: {
      // disabled: true // Removed - not supported in this version
    }
  })
}
