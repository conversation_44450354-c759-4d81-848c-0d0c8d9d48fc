"use client"

import { useState, useEffect, useRef } from "react"
import { Plus, Edit, Trash2, Package, Tag, Clock, Upload, X, Image } from "lucide-react"
import { Product } from "../../../types"

export default function ProductManagement() {
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingProduct, setEditingProduct] = useState<Product | null>(null)
  const [formData, setFormData] = useState({
    name: "",
    price: "",
    distributorPrice: "",
    hasOffer: false,
    originalPrice: "",
    discountPercentage: "",
    offerStartDate: "",
    offerEndDate: "",
    sortOrder: "",
    popular: false,
    specialOffer: false,
    imageUrl: "",
  })
  const [submitting, setSubmitting] = useState(false)
  
  // Image upload states
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [imageUploading, setImageUploading] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/admin/products')
        
        if (response.status === 401) {
          setError('غير مسموح بالوصول - يرجى تسجيل الدخول')
          return
        }
        
        if (response.status === 403) {
          setError('ليس لديك صلاحيات إدارية')
          return
        }
        
        if (!response.ok) {
          throw new Error('Failed to fetch products')
        }
        
        const data = await response.json()
        // Admin API returns products directly, not wrapped in { products: [] }
        setProducts(Array.isArray(data) ? data : data.products || [])
      } catch (error) {
        console.error('Error fetching products:', error)
        setError('حدث خطأ أثناء جلب المنتجات')
      } finally {
        setLoading(false)
      }
    }

    fetchProducts()
  }, [])

  // CRUD Functions
  const handleAddProduct = async () => {
    if (!formData.name || !formData.price) {
      setError('يرجى ملء الحقول المطلوبة (الاسم والسعر)')
      return
    }

    try {
      setSubmitting(true)
      setError(null)

      // Handle image upload first
      const imageUrl = await handleImageUpload()

      const productData: any = {
        name: formData.name,
        price: parseFloat(formData.price),
        distributorPrice: formData.distributorPrice ? parseFloat(formData.distributorPrice) : parseFloat(formData.price),
        hasOffer: formData.hasOffer,
        sortOrder: formData.sortOrder ? parseInt(formData.sortOrder) : products.length + 1,
        isActive: true, // New products are active by default
        isPopular: formData.popular,
        specialOffer: formData.specialOffer,
        imageUrl: imageUrl || null,
      }

      // Add offer fields if offer is enabled
      if (formData.hasOffer) {
        if (!formData.originalPrice || !formData.discountPercentage) {
          setError('يرجى ملء جميع حقول العرض')
          return
        }
        productData.originalPrice = parseFloat(formData.originalPrice)
        productData.discountPercentage = parseFloat(formData.discountPercentage)

        // Note: offerStartDate and offerEndDate are not supported in current Supabase schema
        // They will be ignored by the backend
      }

      const response = await fetch('/api/admin/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData),
      })

      if (response.status === 401) {
        setError('غير مسموح بالوصول - يرجى تسجيل الدخول')
        return
      }
      
      if (response.status === 403) {
        setError('ليس لديك صلاحيات إدارية')
        return
      }

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
        throw new Error(errorData.error || 'Failed to create product')
      }

      const newProduct = await response.json()

      // Refresh products list
      const updatedResponse = await fetch('/api/admin/products')
      if (updatedResponse.ok) {
        const updatedData = await updatedResponse.json()
        setProducts(Array.isArray(updatedData) ? updatedData : updatedData.products || [])
      }

      // Reset form
      resetForm()
      setShowAddForm(false)
    } catch (error) {
      console.error('Error adding product:', error)
      setError('حدث خطأ أثناء إضافة المنتج')
    } finally {
      setSubmitting(false)
    }
  }

  const handleEditProduct = (product: Product) => {
    setEditingProduct(product)
    setFormData({
      name: product.name,
      price: product.price.toString(),
      distributorPrice: product.distributorPrice?.toString() || "",
      hasOffer: product.hasOffer || false,
      originalPrice: product.originalPrice?.toString() || "",
      discountPercentage: product.discountPercentage?.toString() || "",
      offerStartDate: product.offerStartDate ?
        new Date(product.offerStartDate).toISOString().slice(0, 16) : "",
      offerEndDate: product.offerEndDate ?
        new Date(product.offerEndDate).toISOString().slice(0, 16) : "",
      sortOrder: product.sortOrder?.toString() || "",
      popular: product.popular || false,
      specialOffer: product.specialOffer || false,
      imageUrl: product.imageUrl || "",
    })
    
    // Set existing image as preview
    if (product.imageUrl) {
      setImagePreview(product.imageUrl)
    } else {
      setImagePreview(null)
    }
    setImageFile(null)
    
    setShowAddForm(true)
  }

  const handleUpdateProduct = async () => {
    if (!editingProduct || !formData.name || !formData.price) {
      setError('يرجى ملء الحقول المطلوبة (الاسم والسعر)')
      return
    }

    try {
      setSubmitting(true)
      setError(null)

      // Handle image upload if new image was selected
      const imageUrl = await handleImageUpload()

      const updateData: any = {
        name: formData.name,
        price: parseFloat(formData.price),
        distributorPrice: formData.distributorPrice ? parseFloat(formData.distributorPrice) : parseFloat(formData.price),
        hasOffer: formData.hasOffer,
        sortOrder: formData.sortOrder ? parseInt(formData.sortOrder) : undefined,
        isPopular: formData.popular,
        specialOffer: formData.specialOffer,
        imageUrl: imageUrl || formData.imageUrl || null,
      }

      // Add offer fields if offer is enabled
      if (formData.hasOffer) {
        if (!formData.originalPrice || !formData.discountPercentage) {
          setError('يرجى ملء جميع حقول العرض')
          return
        }
        updateData.originalPrice = parseFloat(formData.originalPrice)
        updateData.discountPercentage = parseFloat(formData.discountPercentage)
      } else {
        // Remove offer fields if offer is disabled
        updateData.originalPrice = null
        updateData.discountPercentage = null
      }

      const response = await fetch(`/api/admin/products/${editingProduct.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      })

      if (response.status === 401) {
        setError('غير مسموح بالوصول - يرجى تسجيل الدخول')
        return
      }
      
      if (response.status === 403) {
        setError('ليس لديك صلاحيات إدارية')
        return
      }

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
        throw new Error(errorData.error || 'Failed to update product')
      }

      // Refresh products list
      const updatedResponse = await fetch('/api/admin/products')
      if (updatedResponse.ok) {
        const updatedData = await updatedResponse.json()
        setProducts(Array.isArray(updatedData) ? updatedData : updatedData.products || [])
      }

      // Reset form
      resetForm()
      setEditingProduct(null)
      setShowAddForm(false)
    } catch (error) {
      console.error('Error updating product:', error)
      setError('حدث خطأ أثناء تحديث المنتج')
    } finally {
      setSubmitting(false)
    }
  }

  const handleDeleteProduct = async (productId: string) => {
    if (!confirm("هل أنت متأكد من حذف هذا المنتج؟")) {
      return
    }

    try {
      const response = await fetch(`/api/admin/products/${productId}`, {
        method: 'DELETE',
      })

      if (response.status === 401) {
        setError('غير مسموح بالوصول - يرجى تسجيل الدخول')
        return
      }
      
      if (response.status === 403) {
        setError('ليس لديك صلاحيات إدارية')
        return
      }

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
        throw new Error(errorData.error || 'Failed to delete product')
      }

      // Remove product from local state
      setProducts(products.filter((p) => p.id !== productId))
    } catch (error) {
      console.error('Error deleting product:', error)
      setError('حدث خطأ أثناء حذف المنتج')
    }
  }

  const resetForm = () => {
    setFormData({
      name: "",
      price: "",
      distributorPrice: "",
      hasOffer: false,
      originalPrice: "",
      discountPercentage: "",
      offerStartDate: "",
      offerEndDate: "",
      sortOrder: "",
      popular: false,
      specialOffer: false,
      imageUrl: "",
    })
    setImageFile(null)
    setImagePreview(null)
    setEditingProduct(null)
    setError(null)
  }

  // Image upload functions
  const handleImageSelect = (file: File) => {
    if (!file.type.startsWith('image/')) {
      setError('يرجى اختيار ملف صورة صالح')
      return
    }

    const maxSize = 5 * 1024 * 1024 // 5MB
    if (file.size > maxSize) {
      setError('حجم الصورة كبير جداً. الحد الأقصى 5 ميجابايت')
      return
    }

    setImageFile(file)
    setError(null)

    // Create preview
    const reader = new FileReader()
    reader.onload = (e) => {
      setImagePreview(e.target?.result as string)
    }
    reader.readAsDataURL(file)
  }

  const handleImageUpload = async (): Promise<string | null> => {
    if (!imageFile) return formData.imageUrl || null

    try {
      setImageUploading(true)
      const uploadFormData = new FormData()
      uploadFormData.append('image', imageFile)

      const response = await fetch('/api/admin/products/upload-image', {
        method: 'POST',
        body: uploadFormData,
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'فشل في رفع الصورة')
      }

      const result = await response.json()
      return result.imageUrl
    } catch (error) {
      console.error('Error uploading image:', error)
      setError('فشل في رفع الصورة')
      return null
    } finally {
      setImageUploading(false)
    }
  }

  const handleRemoveImage = async (imageUrl?: string) => {
    // If there's an existing image URL, delete it from server
    // Support both old local paths and new Supabase URLs
    if (imageUrl && (imageUrl.startsWith('/uploads/') || imageUrl.includes('supabase'))) {
      try {
        await fetch('/api/admin/products/upload-image', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ imageUrl }),
        })
      } catch (error) {
        console.log('Failed to delete image from server:', error)
      }
    }

    setImageFile(null)
    setImagePreview(null)
    setFormData({ ...formData, imageUrl: "" })
  }

  const handleDragEvents = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }

  const handleDragEnter = (e: React.DragEvent) => {
    handleDragEvents(e)
    setDragActive(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    handleDragEvents(e)
    setDragActive(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    handleDragEvents(e)
    setDragActive(false)

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleImageSelect(files[0])
    }
  }

  // Helper functions
  const isOfferActive = (product: Product) => {
    if (!product.hasOffer) return false

    const now = new Date()
    const startDate = product.offerStartDate ? new Date(product.offerStartDate) : null
    const endDate = product.offerEndDate ? new Date(product.offerEndDate) : null

    if (startDate && now < startDate) return false
    if (endDate && now > endDate) return false

    return true
  }

  const getPriceDisplayInfo = (product: Product) => {
    const hasActiveOffer = isOfferActive(product)

    if (hasActiveOffer && product.originalPrice && product.discountPercentage) {
      const discountedPrice = product.originalPrice * (1 - product.discountPercentage / 100)
      return {
        currentPrice: discountedPrice,
        originalPrice: product.originalPrice,
        hasDiscount: true
      }
    }

    return {
      currentPrice: product.price,
      originalPrice: null,
      hasDiscount: false
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="bg-gray-800 border border-gray-600 rounded-lg p-6">
          <div className="text-center py-8">
            <div className="text-white">جاري تحميل المنتجات...</div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-gray-800 border border-gray-600 rounded-lg p-6">
          <div className="text-center py-8">
            <div className="text-red-400 mb-4">{error}</div>
            <button
              onClick={() => {
                if (typeof window !== 'undefined') {
                  window.location.reload()
                }
              }}
              className="bg-red-800 hover:bg-red-900 text-white px-4 py-2 rounded-lg"
            >
              إعادة المحاولة
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="bg-gray-800 border border-gray-600 rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-white">إدارة المنتجات</h2>
          <button
            onClick={() => {
              resetForm()
              setEditingProduct(null)
              setShowAddForm(true)
            }}
            className="bg-red-800 hover:bg-red-900 text-white px-4 py-2 rounded-lg transition-colors flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            إضافة منتج جديد
          </button>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {products.map((product) => {
            const priceInfo = getPriceDisplayInfo(product)
            const hasActiveOffer = isOfferActive(product)

            return (
              <div key={product.id} className="bg-gray-900 border border-gray-600 rounded-lg overflow-hidden">
                {/* Product Image Header */}
                {product.imageUrl ? (
                  <div className="relative h-32 bg-gray-800">
                    <img
                      src={product.imageUrl}
                      alt={product.name}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        // Fallback if image fails to load
                        e.currentTarget.style.display = 'none'
                        e.currentTarget.nextElementSibling?.classList.remove('hidden')
                      }}
                    />
                    <div 
                      className="hidden w-full h-full flex items-center justify-center bg-gray-800"
                    >
                      <Image className="w-8 h-8 text-gray-500" />
                    </div>
                    {/* Image indicator badge */}
                    <div className="absolute top-2 left-2 bg-blue-600 text-white px-2 py-1 rounded text-xs font-bold flex items-center gap-1">
                      <Image className="w-3 h-3" />
                      صورة
                    </div>
                  </div>
                ) : (
                  <div className="h-32 bg-gray-800 flex items-center justify-center border-b border-gray-600">
                    <div className="text-center">
                      <Package className="w-8 h-8 text-gray-500 mx-auto mb-1" />
                      <span className="text-xs text-gray-500">بدون صورة</span>
                    </div>
                  </div>
                )}

                <div className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2 flex-wrap">
                      <span
                        className={`px-2 py-1 rounded text-xs ${
                          product.isActive ? "bg-green-500/20 text-green-400" : "bg-red-500/20 text-red-400"
                        }`}
                      >
                        {product.isActive ? "نشط" : "معطل"}
                      </span>
                      {hasActiveOffer && (
                        <span className="px-2 py-1 rounded text-xs bg-orange-500/20 text-orange-400 flex items-center gap-1">
                          <Tag className="w-3 h-3" />
                          عرض
                        </span>
                      )}
                      {product.popular && (
                        <span className="px-2 py-1 rounded text-xs bg-orange-500/20 text-orange-400">
                          الأكثر شعبية
                        </span>
                      )}
                      {product.specialOffer && (
                        <span className="px-2 py-1 rounded text-xs bg-green-500/20 text-green-400">
                          عرض خاص
                        </span>
                      )}
                    </div>
                    <div className="flex items-center gap-1">
                      <button
                        onClick={() => handleEditProduct(product)}
                        className="p-1 hover:bg-gray-700 rounded transition-colors"
                        title="تعديل"
                      >
                        <Edit className="w-4 h-4 text-gray-400" />
                      </button>
                      <button
                        onClick={() => handleDeleteProduct(product.id)}
                        className="p-1 hover:bg-gray-700 rounded transition-colors"
                        title="حذف"
                      >
                        <Trash2 className="w-4 h-4 text-red-400" />
                      </button>
                    </div>
                  </div>

                  <h3 className="text-white font-semibold mb-2">{product.name}</h3>

                {/* Price Display */}
                <div className="mb-3">
                  <div className="space-y-2">
                    {/* Regular Price */}
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-400">سعر عادي:</span>
                      <span className="text-red-400 font-bold">${product.price.toFixed(2)}</span>
                    </div>
                    
                    {/* Distributor Price */}
                    {product.distributorPrice && product.distributorPrice !== product.price && (
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-400">سعر موزع:</span>
                        <span className="text-orange-400 font-bold">${product.distributorPrice.toFixed(2)}</span>
                      </div>
                    )}

                    {/* Offer Price */}
                    {hasActiveOffer && priceInfo.originalPrice && (
                      <div className="space-y-1 pt-2 border-t border-gray-700">
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-400">سعر العرض:</span>
                          <span className="text-green-400 font-bold">${priceInfo.currentPrice.toFixed(2)}</span>
                        </div>
                        <div className="text-xs text-orange-400">
                          خصم {product.discountPercentage}% من ${priceInfo.originalPrice.toFixed(2)}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Offer Status */}
                {product.hasOffer && (
                  <div className="mb-3 p-2 bg-gray-800 rounded text-xs">
                    <div className="flex items-center gap-1 text-orange-400 mb-1">
                      <Tag className="w-3 h-3" />
                      <span>عرض خاص</span>
                    </div>
                    <div className="text-gray-400">
                      السعر الأصلي: ${product.originalPrice?.toFixed(2) || 'غير محدد'}
                    </div>
                    <div className="text-gray-400">
                      نسبة الخصم: {product.discountPercentage || 0}%
                    </div>
                    <div className="text-green-400 mt-1">
                      العرض نشط
                    </div>
                  </div>
                )}
                </div> {/* Close the p-4 div */}
              </div>
            )
          })}
        </div>

        {products.length === 0 && (
          <div className="text-center py-8">
            <Package className="w-16 h-16 text-gray-600 mx-auto mb-4" />
            <div className="text-gray-400">لا توجد منتجات متاحة</div>
          </div>
        )}
      </div>

      {/* Add/Edit Product Modal */}
      {showAddForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-gray-800 border border-gray-600 rounded-lg p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-bold text-white mb-4">
              {editingProduct ? "تعديل المنتج" : "إضافة منتج جديد"}
            </h3>

            {error && (
              <div className="bg-red-500/20 border border-red-500 text-red-400 px-4 py-2 rounded mb-4">
                {error}
              </div>
            )}

            <div className="space-y-4">
              {/* Basic Product Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">اسم المنتج *</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-800"
                    placeholder="مثال: 💎 110 جوهرة"
                    disabled={submitting}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">السعر العادي ($) *</label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.price}
                    onChange={(e) => setFormData({ ...formData, price: e.target.value })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-800"
                    placeholder="0.00"
                    disabled={submitting}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">سعر الموزعين ($)</label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.distributorPrice}
                    onChange={(e) => setFormData({ ...formData, distributorPrice: e.target.value })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-800"
                    placeholder="0.00 (اتركه فارغاً لاستخدام السعر العادي)"
                    disabled={submitting}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    السعر الخاص للموزعين. اتركه فارغاً لاستخدام نفس السعر العادي.
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">ترتيب العرض</label>
                  <input
                    type="number"
                    min="0"
                    value={formData.sortOrder}
                    onChange={(e) => setFormData({ ...formData, sortOrder: e.target.value })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-800"
                    placeholder="0"
                    disabled={submitting}
                  />
                </div>
              </div>

              {/* Image Upload Section */}
              <div className="border-t border-gray-600 pt-4">
                <label className="block text-sm font-medium text-gray-300 mb-3 flex items-center gap-2">
                  <Image className="w-4 h-4" />
                  صورة المنتج
                </label>
                
                {/* Image Preview */}
                {imagePreview && (
                  <div className="mb-4 relative">
                    <div className="w-32 h-32 rounded-lg overflow-hidden border-2 border-gray-600 bg-gray-700">
                      <img
                        src={imagePreview}
                        alt="Product preview"
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <button
                      type="button"
                      onClick={() => handleRemoveImage(formData.imageUrl)}
                      className="absolute -top-2 -right-2 bg-red-600 hover:bg-red-700 text-white rounded-full w-6 h-6 flex items-center justify-center transition-colors"
                      disabled={submitting || imageUploading}
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                )}

                {/* Upload Area */}
                <div
                  className={`
                    border-2 border-dashed rounded-lg p-6 text-center transition-colors cursor-pointer
                    ${dragActive ? 'border-red-500 bg-red-500/10' : 'border-gray-600 hover:border-gray-500'}
                    ${submitting || imageUploading ? 'opacity-50 cursor-not-allowed' : ''}
                  `}
                  onDragEnter={handleDragEnter}
                  onDragLeave={handleDragLeave}
                  onDragOver={handleDragEvents}
                  onDrop={handleDrop}
                  onClick={() => !submitting && !imageUploading && fileInputRef.current?.click()}
                >
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/jpeg,image/jpg,image/png,image/webp"
                    onChange={(e) => {
                      const file = e.target.files?.[0]
                      if (file) handleImageSelect(file)
                    }}
                    className="hidden"
                    disabled={submitting || imageUploading}
                  />
                  
                  <div className="space-y-2">
                    <Upload className="w-8 h-8 text-gray-400 mx-auto" />
                    <div className="text-gray-300">
                      {imageUploading ? (
                        <div className="flex items-center justify-center gap-2">
                          <div className="w-4 h-4 border-2 border-red-500 border-t-transparent rounded-full animate-spin"></div>
                          جاري رفع الصورة...
                        </div>
                      ) : (
                        <>
                          <div>اسحب الصورة هنا أو انقر للاختيار</div>
                          <div className="text-xs text-gray-500 mt-1">
                            JPEG, PNG, WebP - حد أقصى 5 ميجابايت
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </div>

                <div className="text-xs text-gray-500 mt-2">
                  ملاحظة: الصورة ستظهر كخلفية في بطاقات المنتجات على الصفحة الرئيسية
                </div>
              </div>

              {/* Offer Section */}
              <div className="border-t border-gray-600 pt-4">
                <div className="flex items-center gap-2 mb-4">
                  <input
                    type="checkbox"
                    id="hasOffer"
                    checked={formData.hasOffer}
                    onChange={(e) => setFormData({ ...formData, hasOffer: e.target.checked })}
                    className="w-4 h-4 text-red-600 bg-gray-700 border-gray-600 rounded focus:ring-red-500"
                    disabled={submitting}
                  />
                  <label htmlFor="hasOffer" className="text-sm font-medium text-gray-300 flex items-center gap-2">
                    <Tag className="w-4 h-4" />
                    تفعيل عرض خاص
                  </label>
                </div>

                {formData.hasOffer && (
                  <div className="space-y-4 bg-gray-900 p-4 rounded-lg">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">السعر الأصلي ($) *</label>
                        <input
                          type="number"
                          step="0.01"
                          min="0"
                          value={formData.originalPrice}
                          onChange={(e) => setFormData({ ...formData, originalPrice: e.target.value })}
                          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-800"
                          placeholder="0.00"
                          disabled={submitting}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">نسبة الخصم (%) *</label>
                        <input
                          type="number"
                          min="1"
                          max="100"
                          value={formData.discountPercentage}
                          onChange={(e) => setFormData({ ...formData, discountPercentage: e.target.value })}
                          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-800"
                          placeholder="50"
                          disabled={submitting}
                        />
                      </div>
                    </div>

                    {/* Note: Offer dates are not supported in current schema */}
                    <div className="text-xs text-gray-500 mt-2">
                      ملاحظة: تواريخ العروض غير مدعومة حالياً في النظام
                    </div>

                    {/* Price Preview */}
                    {formData.originalPrice && formData.discountPercentage && (
                      <div className="bg-gray-800 p-3 rounded border border-gray-600">
                        <div className="text-sm text-gray-300 mb-2">معاينة السعر:</div>
                        <div className="flex items-center gap-4">
                          <span className="text-gray-400 line-through">${Number(formData.originalPrice).toFixed(2)}</span>
                          <span className="text-green-400 font-bold">
                            ${(Number(formData.originalPrice) * (1 - Number(formData.discountPercentage) / 100)).toFixed(2)}
                          </span>
                          <span className="text-orange-400 text-sm">
                            خصم {formData.discountPercentage}%
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* Product Display Settings */}
                <div className="space-y-4 bg-gray-900 p-4 rounded-lg">
                  <h4 className="text-lg font-medium text-white mb-3">إعدادات العرض</h4>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Popular Badge Toggle */}
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <input
                        type="checkbox"
                        id="popular"
                        checked={formData.popular}
                        onChange={(e) => setFormData({ ...formData, popular: e.target.checked })}
                        className="w-4 h-4 text-red-600 bg-gray-700 border-gray-600 rounded focus:ring-red-500 focus:ring-2"
                        disabled={submitting}
                      />
                      <label htmlFor="popular" className="text-sm font-medium text-gray-300">
                        الأكثر شعبية
                      </label>
                      <span className="text-xs text-gray-500">(يظهر شارة "الأكثر شعبية")</span>
                    </div>

                    {/* Special Offer Badge Toggle */}
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <input
                        type="checkbox"
                        id="specialOffer"
                        checked={formData.specialOffer}
                        onChange={(e) => setFormData({ ...formData, specialOffer: e.target.checked })}
                        className="w-4 h-4 text-red-600 bg-gray-700 border-gray-600 rounded focus:ring-red-500 focus:ring-2"
                        disabled={submitting}
                      />
                      <label htmlFor="specialOffer" className="text-sm font-medium text-gray-300">
                        عرض خاص
                      </label>
                      <span className="text-xs text-gray-500">(يظهر شارة "عرض خاص")</span>
                    </div>
                  </div>

                  <div className="text-xs text-gray-500 mt-2">
                    ملاحظة: هذه الإعدادات تتحكم في الشارات التي تظهر على بطاقات المنتجات في الصفحة الرئيسية
                  </div>
                </div>
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <button
                onClick={() => {
                  setShowAddForm(false)
                  setEditingProduct(null)
                  resetForm()
                }}
                className="flex-1 bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded transition-colors"
                disabled={submitting}
              >
                إلغاء
              </button>
              <button
                onClick={editingProduct ? handleUpdateProduct : handleAddProduct}
                className="flex-1 bg-red-800 hover:bg-red-900 text-white py-2 px-4 rounded transition-colors disabled:opacity-50"
                disabled={submitting}
              >
                {submitting ? "جاري الحفظ..." : (editingProduct ? "تحديث" : "إضافة")}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
