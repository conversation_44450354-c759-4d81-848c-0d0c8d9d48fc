"use client"

import { useState, useEffect } from "react"
import {
  Edit, Shield, DollarSign, Wallet, Save, X,
  Eye, EyeOff, Lock, Mail, User, Calendar, CheckCircle,
  AlertCircle, Settings, Download, MessageCircle, Key,
  Camera, Upload, ChevronDown, ChevronUp
} from "lucide-react"
import { useSupabaseAuth } from "../../contexts/SupabaseAuthContext"
import CopyButton from "../components/CopyButton"
import AccountStatusGuard from "../../components/AccountStatusGuard"

import Image from "next/image"
import { formatFirebaseDate } from "../../lib/utils/dateUtils"

function ProfilePage() {
  const { user, updateProfile, changePassword } = useSupabaseAuth()

  // State management
  const [isEditing, setIsEditing] = useState(false)
  const [isChangingPassword, setIsChangingPassword] = useState(false)
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)
  const [isClient, setIsClient] = useState(false)
  const [isDesktop, setIsDesktop] = useState(false)

  // Mobile-specific state for collapsible sections
  const [expandedSections, setExpandedSections] = useState({
    personalInfo: true,
    accountInfo: true,
    passwordSection: false
  })

  // Handle client-side hydration and responsive behavior
  useEffect(() => {
    setIsClient(true)

    const checkScreenSize = () => {
      setIsDesktop(window.innerWidth >= 768)
    }

    // Initial check
    checkScreenSize()

    // Add resize listener
    window.addEventListener('resize', checkScreenSize)

    return () => {
      window.removeEventListener('resize', checkScreenSize)
    }
  }, [])

  // Form data
  const [formData, setFormData] = useState({
    name: user?.name || '',
  })

  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  })

  const [whatsAppNumber, setWhatsAppNumber] = useState<string>("+************")

  // Toggle section expansion
  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }



  // Fetch WhatsApp number from settings API
  useEffect(() => {
    const fetchWhatsAppNumber = async () => {
      try {
        const response = await fetch('/api/settings')
        if (response.ok) {
          const settings = await response.json()
          if (settings && settings.supportWhatsApp) {
            setWhatsAppNumber(settings.supportWhatsApp)
          }
        }
      } catch (error) {
        console.warn('Could not fetch WhatsApp number from settings, using default:', error)
      }
    }

    fetchWhatsAppNumber()
  }, [])



  // Handler functions
  const handleSaveProfile = async () => {
    if (!formData.name.trim()) {
      setMessage({ type: 'error', text: 'يرجى إدخال الاسم' })
      return
    }

    setLoading(true)
    try {
      await updateProfile({ name: formData.name })
      setIsEditing(false)
      setMessage({ type: 'success', text: 'تم تحديث الملف الشخصي بنجاح' })
    } catch (error: any) {
      setMessage({ type: 'error', text: error.message || 'حدث خطأ أثناء التحديث' })
    } finally {
      setLoading(false)
    }
  }

  const handleChangePassword = async () => {
    if (!passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword) {
      setMessage({ type: 'error', text: 'يرجى ملء جميع الحقول' })
      return
    }

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setMessage({ type: 'error', text: 'كلمة المرور الجديدة غير متطابقة' })
      return
    }

    if (passwordData.newPassword.length < 6) {
      setMessage({ type: 'error', text: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل' })
      return
    }

    setLoading(true)
    try {
      await changePassword(passwordData.currentPassword, passwordData.newPassword)
      setIsChangingPassword(false)
      setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' })
      setMessage({ type: 'success', text: 'تم تغيير كلمة المرور بنجاح' })
    } catch (error: any) {
      setMessage({ type: 'error', text: error.message || 'حدث خطأ أثناء تغيير كلمة المرور' })
    } finally {
      setLoading(false)
    }
  }



  const clearMessage = () => {
    setTimeout(() => setMessage(null), 5000)
  }

  // Clear message after 5 seconds
  if (message) {
    clearMessage()
  }

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl min-h-screen">
        <div className="text-center text-white">جاري التحميل...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      <div className="container mx-auto px-4 py-6 max-w-4xl">


        {/* Success/Error Messages */}
        {message && (
          <div className={`mb-6 p-4 rounded-xl border backdrop-blur-sm animate-fadeIn ${
            message.type === 'success'
              ? 'bg-green-500/10 border-green-500/30 text-green-400'
              : 'bg-red-500/10 border-red-500/30 text-red-400'
          }`}>
            <div className="flex items-center gap-3">
              {message.type === 'success' ? (
                <CheckCircle className="w-5 h-5 flex-shrink-0" />
              ) : (
                <AlertCircle className="w-5 h-5 flex-shrink-0" />
              )}
              <span className="text-sm md:text-base">{message.text}</span>
            </div>
          </div>
        )}

        {/* Mobile-First Layout */}
        <div className="space-y-6">
          {/* Personal Information Card */}
          <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-600/50 rounded-2xl overflow-hidden shadow-2xl">
            <div className="p-6 flex items-center justify-between hover:bg-gray-700/30 transition-all duration-200">
              <button
                onClick={() => toggleSection('personalInfo')}
                className="flex items-center gap-4 flex-1 text-left md:cursor-default"
              >
                <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-blue-800 rounded-xl flex items-center justify-center shadow-lg">
                  <User className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-lg md:text-xl font-bold text-white">المعلومات الشخصية</h2>
                  <p className="text-gray-400 text-sm">إدارة بياناتك الأساسية</p>
                </div>
              </button>
              <div className="flex items-center gap-3">
                {isEditing && (
                  <button
                    onClick={() => {
                      setIsEditing(false)
                      setFormData({ name: user.name })
                    }}
                    className="flex items-center gap-2 bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded-lg transition-all duration-200 text-sm"
                  >
                    <X className="w-4 h-4" />
                    <span className="hidden sm:inline">إلغاء</span>
                  </button>
                )}
                <button
                  onClick={() => {
                    isEditing ? handleSaveProfile() : setIsEditing(true)
                  }}
                  disabled={loading}
                  className="flex items-center gap-2 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 disabled:opacity-50 text-white px-4 py-2 rounded-lg transition-all duration-200 shadow-lg text-sm"
                >
                  {loading ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : isEditing ? (
                    <Save className="w-4 h-4" />
                  ) : (
                    <Edit className="w-4 h-4" />
                  )}
                  <span className="hidden sm:inline">{isEditing ? "حفظ" : "تعديل"}</span>
                </button>
                <button
                  onClick={() => toggleSection('personalInfo')}
                  className="md:hidden"
                >
                  <ChevronDown className={`w-5 h-5 text-gray-400 transition-transform duration-200 ${
                    expandedSections.personalInfo ? 'rotate-180' : ''
                  }`} />
                </button>
              </div>
            </div>

            {(expandedSections.personalInfo || (isClient && isDesktop)) && (
              <div className="px-6 pb-6 space-y-6 animate-fadeIn">
                {/* Name Field */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-3 flex items-center gap-2">
                    <User className="w-4 h-4 text-blue-400" />
                    الاسم الكامل
                    {isEditing && <span className="text-red-400 text-xs">*</span>}
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      value={isEditing ? formData.name : user.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      className={`w-full px-4 py-4 pr-12 bg-gray-900/50 border rounded-xl text-white transition-all duration-300 text-base ${
                        isEditing
                          ? 'border-blue-500 focus:border-blue-400 focus:ring-2 focus:ring-blue-500/20 shadow-lg bg-gray-900'
                          : 'border-gray-600/50 cursor-not-allowed'
                      } focus:outline-none`}
                      readOnly={!isEditing}
                      placeholder="أدخل اسمك الكامل"
                    />
                    <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                      {isEditing ? (
                        <Edit className="w-4 h-4 text-blue-400" />
                      ) : (
                        <User className="w-4 h-4 text-gray-500" />
                      )}
                    </div>
                  </div>
                  {isEditing && (
                    <p className="text-xs text-blue-400 mt-2 flex items-center gap-1 animate-fadeIn">
                      <span>💡</span>
                      سيظهر هذا الاسم في جميع أنحاء الموقع
                    </p>
                  )}
                </div>

                {/* Email Field (Read-only) */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-3 flex items-center gap-2">
                    <Mail className="w-4 h-4 text-gray-400" />
                    البريد الإلكتروني
                    <span className="text-xs text-gray-500 bg-gray-700/50 px-2 py-1 rounded-full border border-gray-600/50">للقراءة فقط</span>
                  </label>
                  <div className="relative">
                    <input
                      type="email"
                      value={user.email}
                      className="w-full px-4 py-4 pr-12 bg-gray-800/30 border border-gray-600/50 rounded-xl text-gray-300 cursor-not-allowed focus:outline-none text-base"
                      readOnly
                    />
                    <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                      <Shield className="w-4 h-4 text-gray-500" />
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 mt-2 flex items-center gap-1">
                    <span>🔒</span>
                    لا يمكن تغيير البريد الإلكتروني لأسباب أمنية
                  </p>
                </div>
              </div>
            )}

          </div>

          {/* Account Information Card */}
          <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-600/50 rounded-2xl overflow-hidden shadow-2xl">
            <button
              onClick={() => toggleSection('accountInfo')}
              className="w-full p-6 flex items-center justify-between hover:bg-gray-700/30 transition-all duration-200 md:cursor-default"
            >
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-600 to-purple-800 rounded-xl flex items-center justify-center shadow-lg">
                  <Shield className="w-6 h-6 text-white" />
                </div>
                <div className="text-left">
                  <h2 className="text-lg md:text-xl font-bold text-white">معلومات الحساب</h2>
                  <p className="text-gray-400 text-sm">بيانات الحساب والإحصائيات</p>
                </div>
              </div>
              <ChevronDown className={`w-5 h-5 text-gray-400 transition-transform duration-200 md:hidden ${
                expandedSections.accountInfo ? 'rotate-180' : ''
              }`} />
            </button>

            {(expandedSections.accountInfo || (isClient && isDesktop)) && (
              <div className="px-6 pb-6 space-y-6 animate-fadeIn">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* User ID */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3 flex items-center gap-2">
                      <Shield className="w-4 h-4 text-purple-400" />
                      معرف المستخدم
                      <span className="text-xs text-purple-400 bg-purple-500/20 px-2 py-1 rounded-full border border-purple-500/30">فريد</span>
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        value={user.id}
                        className="w-full px-4 py-4 pr-12 bg-gray-900/50 border border-gray-600/50 rounded-xl text-white font-mono text-sm cursor-not-allowed focus:outline-none"
                        readOnly
                      />
                      <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                        <CopyButton text={user.id} size="md" />
                      </div>
                    </div>
                    <p className="text-xs text-purple-400 mt-2 flex items-center gap-1">
                      <span>🆔</span>
                      استخدم هذا المعرف للدعم الفني وشحن الرصيد
                    </p>
                  </div>

                  {/* Registration Date */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3 flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-green-400" />
                      تاريخ التسجيل
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        value={formatFirebaseDate(user.createdAt)}
                        className="w-full px-4 py-4 pr-12 bg-gray-900/50 border border-gray-600/50 rounded-xl text-white cursor-not-allowed focus:outline-none text-sm"
                        readOnly
                      />
                      <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                        <Calendar className="w-4 h-4 text-green-400" />
                      </div>
                    </div>
                    <p className="text-xs text-green-400 mt-2 flex items-center gap-1">
                      <span>📅</span>
                      عضو منذ {formatFirebaseDate(user.createdAt)}
                    </p>
                  </div>
                </div>

                {/* Account Status & Statistics Section */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Account Status */}
                  <div className={`border rounded-xl p-5 shadow-lg ${
                    user.status === "active"
                      ? "bg-gradient-to-br from-green-500/10 to-emerald-500/10 border-green-500/30"
                      : user.status === "pending"
                        ? "bg-gradient-to-br from-yellow-500/10 to-orange-500/10 border-yellow-500/30"
                        : "bg-gradient-to-br from-red-500/10 to-pink-500/10 border-red-500/30"
                  }`}>
                    <div className="flex items-center gap-3 mb-4">
                      <div className={`w-10 h-10 rounded-lg flex items-center justify-center shadow-lg ${
                        user.status === "active" ? "bg-green-500" : user.status === "pending" ? "bg-yellow-500" : "bg-red-500"
                      }`}>
                        <CheckCircle className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h3 className={`font-semibold ${
                          user.status === "active" ? "text-green-400" : user.status === "pending" ? "text-yellow-400" : "text-red-400"
                        }`}>
                          حالة الحساب
                        </h3>
                        <p className="text-gray-400 text-xs">الوضع الحالي</p>
                      </div>
                    </div>
                    <div className="text-center">
                      <div className={`text-lg font-bold mb-1 ${
                        user.status === "active" ? "text-green-400" : user.status === "pending" ? "text-yellow-400" : "text-red-400"
                      }`}>
                        {user.status === "active" ? "✅ نشط ومفعل" : user.status === "pending" ? "⏳ قيد المراجعة" : "❌ معلق"}
                      </div>
                      <p className={`text-xs ${
                        user.status === "active" ? "text-green-300" : user.status === "pending" ? "text-yellow-300" : "text-red-300"
                      }`}>
                        {user.status === "active"
                          ? "يمكنك استخدام جميع الخدمات"
                          : user.status === "pending"
                            ? "في انتظار موافقة الإدارة"
                            : "تواصل مع الدعم الفني"
                        }
                      </p>
                    </div>
                  </div>


                </div>

                {/* Quick Actions */}
                <div className="space-y-3">
                  <button
                    onClick={() => {
                      const message = `🔥 *استفسار عام - نيران كارد*\n\n` +
                        `مرحباً، أحتاج للمساعدة:\n\n` +
                        `👤 *معرف المستخدم:* ${user.id}\n` +
                        `📧 *الاسم:* ${user.name}\n` +
                        `📧 *البريد الإلكتروني:* ${user.email}\n\n` +
                        `شكراً لكم 🙏`

                      const phoneNumber = whatsAppNumber.replace(/\+/g, '')
                      const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`
                      window.open(whatsappUrl, '_blank')
                    }}
                    className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white py-4 px-4 rounded-xl transition-all duration-200 flex items-center gap-3 group shadow-lg"
                  >
                    <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                      <MessageCircle className="w-4 h-4 group-hover:scale-110 transition-transform" />
                    </div>
                    <div className="text-left flex-1">
                      <div className="font-medium">الدعم الفني</div>
                      <div className="text-xs text-green-200">تواصل معنا للمساعدة</div>
                    </div>
                  </button>
                </div>


              </div>
            )}
          </div>

          {/* Password Management Card */}
          <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-600/50 rounded-2xl overflow-hidden shadow-2xl">
            <div className="p-6 flex items-center justify-between hover:bg-gray-700/30 transition-all duration-200">
              <button
                onClick={() => toggleSection('passwordSection')}
                className="flex items-center gap-4 flex-1 text-left md:cursor-default"
              >
                <div className="w-12 h-12 bg-gradient-to-br from-indigo-600 to-indigo-800 rounded-xl flex items-center justify-center shadow-lg">
                  <Lock className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-lg md:text-xl font-bold text-white">إدارة كلمة المرور</h2>
                  <p className="text-gray-400 text-sm">حافظ على أمان حسابك</p>
                </div>
              </button>
              <div className="flex items-center gap-3">
                {isChangingPassword && (
                  <button
                    onClick={() => {
                      setIsChangingPassword(false)
                      setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' })
                    }}
                    className="flex items-center gap-2 bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded-lg transition-all duration-200 text-sm"
                  >
                    <X className="w-4 h-4" />
                    <span className="hidden sm:inline">إلغاء</span>
                  </button>
                )}
                <button
                  onClick={() => {
                    isChangingPassword ? handleChangePassword() : setIsChangingPassword(true)
                  }}
                  disabled={loading}
                  className="flex items-center gap-2 bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-700 hover:to-indigo-800 disabled:opacity-50 text-white px-4 py-2 rounded-lg transition-all duration-200 shadow-lg text-sm"
                >
                  {loading ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : isChangingPassword ? (
                    <Save className="w-4 h-4" />
                  ) : (
                    <Key className="w-4 h-4" />
                  )}
                  <span className="hidden sm:inline">{isChangingPassword ? "حفظ كلمة المرور" : "تغيير كلمة المرور"}</span>
                </button>
                <button
                  onClick={() => toggleSection('passwordSection')}
                  className="md:hidden"
                >
                  <ChevronDown className={`w-5 h-5 text-gray-400 transition-transform duration-200 ${
                    expandedSections.passwordSection ? 'rotate-180' : ''
                  }`} />
                </button>
              </div>
            </div>

            {((expandedSections.passwordSection || (isClient && isDesktop)) && isChangingPassword) && (
              <div className="px-6 pb-6 space-y-6 animate-fadeIn">
                {/* Current Password */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-3 flex items-center gap-2">
                    <Lock className="w-4 h-4 text-orange-400" />
                    كلمة المرور الحالية
                    <span className="text-red-400 text-xs">*</span>
                  </label>
                  <div className="relative">
                    <input
                      type={showCurrentPassword ? "text" : "password"}
                      value={passwordData.currentPassword}
                      onChange={(e) => setPasswordData(prev => ({ ...prev, currentPassword: e.target.value }))}
                      className="w-full px-4 py-4 pr-12 bg-gray-900/50 border border-gray-600/50 rounded-xl text-white focus:border-orange-500 focus:ring-2 focus:ring-orange-500/20 focus:outline-none transition-all duration-300 text-base"
                      placeholder="أدخل كلمة المرور الحالية"
                    />
                    <button
                      type="button"
                      onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                      className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-orange-400 transition-colors p-1"
                    >
                      {showCurrentPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>
                  </div>
                </div>

                {/* New Password Fields */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* New Password */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3 flex items-center gap-2">
                      <Key className="w-4 h-4 text-blue-400" />
                      كلمة المرور الجديدة
                      <span className="text-red-400 text-xs">*</span>
                    </label>
                    <div className="relative">
                      <input
                        type={showNewPassword ? "text" : "password"}
                        value={passwordData.newPassword}
                        onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                        className="w-full px-4 py-4 pr-12 bg-gray-900/50 border border-gray-600/50 rounded-xl text-white focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 focus:outline-none transition-all duration-300 text-base"
                        placeholder="أدخل كلمة المرور الجديدة"
                      />
                      <button
                        type="button"
                        onClick={() => setShowNewPassword(!showNewPassword)}
                        className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-blue-400 transition-colors p-1"
                      >
                        {showNewPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                      </button>
                    </div>
                  </div>

                  {/* Confirm Password */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3 flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-400" />
                      تأكيد كلمة المرور
                      <span className="text-red-400 text-xs">*</span>
                    </label>
                    <div className="relative">
                      <input
                        type={showConfirmPassword ? "text" : "password"}
                        value={passwordData.confirmPassword}
                        onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                        className="w-full px-4 py-4 pr-12 bg-gray-900/50 border border-gray-600/50 rounded-xl text-white focus:border-green-500 focus:ring-2 focus:ring-green-500/20 focus:outline-none transition-all duration-300 text-base"
                        placeholder="أعد إدخال كلمة المرور الجديدة"
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-green-400 transition-colors p-1"
                      >
                        {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                      </button>
                    </div>
                  </div>
                </div>

                {/* Password Requirements */}
                <div className="bg-gradient-to-r from-blue-500/10 to-indigo-500/10 border border-blue-500/30 rounded-xl p-5">
                  <h4 className="text-blue-400 font-semibold mb-4 flex items-center gap-2">
                    <Shield className="w-4 h-4" />
                    متطلبات كلمة المرور الآمنة
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center gap-3">
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center transition-all duration-200 ${
                        passwordData.newPassword.length >= 6
                          ? 'bg-green-500 text-white'
                          : 'bg-gray-600 text-gray-400'
                      }`}>
                        {passwordData.newPassword.length >= 6 ? (
                          <CheckCircle className="w-4 h-4" />
                        ) : (
                          <span className="text-xs font-bold">6</span>
                        )}
                      </div>
                      <span className={`text-sm transition-colors ${
                        passwordData.newPassword.length >= 6 ? 'text-green-400' : 'text-gray-400'
                      }`}>
                        6 أحرف على الأقل
                      </span>
                    </div>

                    <div className="flex items-center gap-3">
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center transition-all duration-200 ${
                        passwordData.newPassword === passwordData.confirmPassword && passwordData.newPassword
                          ? 'bg-green-500 text-white'
                          : 'bg-gray-600 text-gray-400'
                      }`}>
                        {passwordData.newPassword === passwordData.confirmPassword && passwordData.newPassword ? (
                          <CheckCircle className="w-4 h-4" />
                        ) : (
                          <span className="text-xs font-bold">✓</span>
                        )}
                      </div>
                      <span className={`text-sm transition-colors ${
                        passwordData.newPassword === passwordData.confirmPassword && passwordData.newPassword
                          ? 'text-green-400'
                          : 'text-gray-400'
                      }`}>
                        تطابق كلمة المرور
                      </span>
                    </div>
                  </div>
                </div>


              </div>
            )}
          </div>




        </div>
      </div>


    </div>
  )
}



// Wrap with authentication and status guard
export default function ProtectedProfilePage() {
  return (
    <AccountStatusGuard>
      <ProfilePage />
    </AccountStatusGuard>
  )
}
