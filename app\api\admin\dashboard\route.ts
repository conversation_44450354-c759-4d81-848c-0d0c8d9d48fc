import { NextRequest, NextResponse } from 'next/server'
import { SupabaseAdminService } from '../../../../lib/services/supabase-admin'

export async function GET(request: NextRequest) {
  try {
    // Get dashboard stats using server-side admin service
    const stats = await SupabaseAdminService.getDashboardStats()

    // Add cache headers for better performance
    return NextResponse.json(stats, {
      headers: {
        'Cache-Control': 'public, max-age=300, s-maxage=300', // 5 minute cache for better performance
        'Content-Type': 'application/json',
      },
    })
  } catch (error: any) {
    // Only log in development
    if (process.env.NODE_ENV === 'development') {
      console.error('❌ Admin API: Error fetching dashboard stats:', error)
    }

    // Return fallback data instead of error to prevent admin dashboard from breaking
    const fallbackStats = {
      totalUsers: 0,
      totalOrders: 0,
      totalRevenue: 0,
      pendingOrders: 0,
      activeUsers: 0,
      pendingUsers: 0,
      completedOrders: 0,
      cancelledOrders: 0,
      activeProducts: 0,
    }

    return NextResponse.json(fallbackStats, {
      headers: {
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/json',
        'X-Fallback-Data': 'true'
      },
    })
  }
}
