/**
 * Supabase Admin Service
 * Replaces Firebase Admin Service with Supabase SQL queries
 * Provides efficient relational database operations
 */

import { supabaseAdmin } from '../supabase-admin'
import { User, Order, Product } from '../../types'

export class SupabaseAdminService {
  private static supabase = supabaseAdmin

  // ==================== DASHBOARD ANALYTICS ====================

  /**
   * Get dashboard statistics using efficient SQL view
   */
  static async getDashboardStats() {
    try {


      // Try to use the regular Supabase client first
      try {
        const { data, error } = await this.supabase
          .from('dashboard_stats')
          .select('*')
          .single()

        if (error) {
          console.error('❌ Dashboard stats error:', error)
          throw error
        }


        return {
          totalUsers: data.total_users,
          totalOrders: data.total_orders,
          totalRevenue: data.total_revenue,
          pendingOrders: data.pending_orders,
          activeUsers: data.active_users,
          pendingUsers: data.pending_users,
          completedOrders: data.completed_orders,
          cancelledOrders: data.cancelled_orders,
          activeProducts: data.active_products,
        }
      } catch (supabaseError) {
        console.warn('⚠️ Regular Supabase client failed, returning fallback data:', supabaseError)

        // Return fallback data instead of throwing error
        return {
          totalUsers: 0,
          totalOrders: 0,
          totalRevenue: 0,
          pendingOrders: 0,
          activeUsers: 0,
          pendingUsers: 0,
          completedOrders: 0,
          cancelledOrders: 0,
          activeProducts: 0,
        }
      }
    } catch (error: any) {
      console.error('❌ Get dashboard stats failed:', error)

      // Return fallback data instead of throwing error
      return {
        totalUsers: 0,
        totalOrders: 0,
        totalRevenue: 0,
        pendingOrders: 0,
        activeUsers: 0,
        pendingUsers: 0,
        completedOrders: 0,
        cancelledOrders: 0,
        activeProducts: 0,
      }
    }
  }

  // ==================== USER MANAGEMENT ====================

  /**
   * Get all users with efficient pagination
   */
  static async getAllUsers(page: number = 1, limit: number = 50): Promise<User[]> {
    try {


      const from = (page - 1) * limit
      const to = from + limit - 1

      const { data, error } = await this.supabase
        .from('user_profiles')
        .select('*')
        .order('created_at', { ascending: false })
        .range(from, to)

      if (error) {
        console.error('❌ Get users error:', error)
        console.warn('⚠️ Returning empty users array due to error')
        return []
      }


      return data.map(this.mapSupabaseUserToAppUser)
    } catch (error: any) {
      console.error('❌ Get all users failed:', error)
      console.warn('⚠️ Returning empty users array due to error')
      return []
    }
  }

  /**
   * ✅ PERFORMANCE: Get paginated users with server-side search and filtering
   */
  static async getAllUsersPaginated(
    page: number = 1, 
    pageSize: number = 20, 
    search: string = '', 
    status: string = 'all'
  ) {
    try {


      const from = (page - 1) * pageSize
      const to = from + pageSize - 1

      let query = this.supabase
        .from('user_profiles')
        .select('*', { count: 'exact' })
        .order('created_at', { ascending: false })
        .range(from, to)

      // ✅ ENHANCED SEARCH: Apply comprehensive search filter
      if (search && search.trim() !== '') {
        const searchTerm = search.trim()
        
        // Check if it looks like a UUID (for exact user ID matches)
        const isUUID = searchTerm.length >= 8 && searchTerm.includes('-')
        
        if (isUUID) {
          // For UUID-like searches, try exact match first, then other fields
          query = query.or(`
            id.eq.${searchTerm},
            name.ilike.%${searchTerm}%,
            email.ilike.%${searchTerm}%
          `.replace(/\s+/g, ''))
        } else {
          // For non-UUID searches, search in all text fields including role and status
          query = query.or(`
            name.ilike.%${searchTerm}%,
            email.ilike.%${searchTerm}%,
            role.ilike.%${searchTerm}%,
            status.ilike.%${searchTerm}%
          `.replace(/\s+/g, ''))
        }
      }

      // Apply status filter
      if (status !== 'all') {
        query = query.eq('status', status)
      }

      const { data, error, count } = await query

      if (error) {
        console.error('❌ Get paginated users error:', error)
        return {
          users: [],
          totalCount: 0,
          totalPages: 0
        }
      }

      const totalCount = count || 0
      const totalPages = Math.ceil(totalCount / pageSize)



      return {
        users: data.map(this.mapSupabaseUserToAppUser),
        totalCount,
        totalPages
      }
    } catch (error: any) {
      console.error('❌ Get paginated users failed:', error)
      return {
        users: [],
        totalCount: 0,
        totalPages: 0
      }
    }
  }

  /**
   * Get user by ID
   */
  static async getUserById(userId: string): Promise<User | null> {
    try {
      console.log(`👤 Fetching user by ID from user_profiles: ${userId}`)

      const { data, error } = await this.supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          console.log(`❌ User ${userId} not found in user_profiles`)
          return null
        }
        console.error('❌ Get user by ID error:', error)
        throw new Error('Failed to fetch user')
      }

      console.log(`✅ User ${userId} fetched successfully from user_profiles`)
      return this.mapSupabaseUserToAppUser(data)
    } catch (error: any) {
      console.error('❌ Get user by ID failed:', error)
      throw new Error('حدث خطأ أثناء جلب بيانات المستخدم')
    }
  }

  /**
   * Update user status (approve, ban, etc.)
   */
  static async updateUserStatus(userId: string, status: 'pending' | 'active' | 'suspended'): Promise<void> {
    try {
      console.log(`🔄 Updating user ${userId} status to ${status}`)

      const { error } = await this.supabase
        .from('user_profiles')
        .update({ status })
        .eq('id', userId)

      if (error) {
        console.error('❌ Update user status error:', error)
        throw new Error('Failed to update user status')
      }

      console.log('✅ User status updated successfully')
    } catch (error: any) {
      console.error('❌ Update user status failed:', error)
      throw new Error('حدث خطأ أثناء تحديث حالة المستخدم')
    }
  }

  /**
   * Update user role with admin protection
   */
  static async updateUserRole(userId: string, role: 'user' | 'admin' | 'distributor'): Promise<void> {
    try {
      console.log(`🔄 Updating user ${userId} role to ${role}`)

      // First, check if the user exists
      const { data: existingUser, error: fetchError } = await this.supabase
        .from('user_profiles')
        .select('id, role, email, name')
        .eq('id', userId)
        .single()

      if (fetchError) {
        console.error('❌ Error fetching user before role update:', fetchError)
        throw new Error(`Failed to fetch user: ${fetchError.message}`)
      }

      if (!existingUser) {
        console.error('❌ User not found:', userId)
        throw new Error('User not found')
      }

      console.log(`📋 Current user details:`, {
        id: existingUser.id,
        currentRole: existingUser.role,
        newRole: role,
        email: existingUser.email,
        name: existingUser.name
      })

      // Attempt the role update
      const { error } = await this.supabase
        .from('user_profiles')
        .update({ role })
        .eq('id', userId)

      if (error) {
        console.error('❌ Update user role error:', error)
        console.error('❌ Error details:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        })
        throw new Error(`Failed to update user role: ${error.message}`)
      }

      // Verify the update worked
      const { data: updatedUser, error: verifyError } = await this.supabase
        .from('user_profiles')
        .select('id, role')
        .eq('id', userId)
        .single()

      if (verifyError) {
        console.error('❌ Error verifying role update:', verifyError)
      } else {
        console.log('✅ Role update verified:', {
          userId: updatedUser.id,
          newRole: updatedUser.role
        })
      }

      console.log('✅ User role updated successfully')
    } catch (error: any) {
      console.error('❌ Update user role failed:', error)
      throw new Error('حدث خطأ أثناء تحديث دور المستخدم')
    }
  }

  /**
   * Update user balance with transaction record
   */
  static async updateUserBalance(userId: string, newBalance: number, adminId?: string, notes?: string): Promise<void> {
    try {
      console.log(`💰 Updating user ${userId} balance to ${newBalance}`)

      // Import TransactionService dynamically to avoid circular dependency
      const { TransactionService } = await import('./transactions')

      // Create transaction record for balance change
      await TransactionService.adminSetBalance(userId, newBalance, adminId, notes)

      console.log('✅ User balance updated successfully with transaction record')
    } catch (error: any) {
      console.error('❌ Update user balance failed:', error)
      throw new Error('حدث خطأ أثناء تحديث رصيد المستخدم')
    }
  }

  /**
   * Add balance to user (creates deposit transaction)
   */
  static async addUserBalance(userId: string, amount: number, adminId: string, notes?: string): Promise<void> {
    try {
      console.log(`💰 Adding ${amount} to user ${userId} balance`)

      const { TransactionService } = await import('./transactions')

      await TransactionService.adminAddBalance(userId, amount, adminId, notes)

      console.log('✅ Balance added successfully with transaction record')
    } catch (error: any) {
      console.error('❌ Add user balance failed:', error)
      throw new Error('حدث خطأ أثناء إضافة رصيد المستخدم')
    }
  }

  /**
   * Deduct balance from user (creates deduction transaction)
   */
  static async deductUserBalance(userId: string, amount: number, adminId: string, notes?: string): Promise<void> {
    try {
      console.log(`💸 Deducting ${amount} from user ${userId} balance`)

      const { TransactionService } = await import('./transactions')

      await TransactionService.adminDeductBalance(userId, amount, adminId, notes)

      console.log('✅ Balance deducted successfully with transaction record')
    } catch (error: any) {
      console.error('❌ Deduct user balance failed:', error)
      throw new Error('حدث خطأ أثناء خصم رصيد المستخدم')
    }
  }

  /**
   * Delete user
   */
  static async deleteUser(userId: string): Promise<void> {
    try {
      console.log(`🗑️ Deleting user ${userId}`)

      const { error } = await this.supabase
        .from('user_profiles')
        .delete()
        .eq('id', userId)

      if (error) {
        console.error('❌ Delete user error:', error)
        throw new Error('Failed to delete user')
      }

      console.log('✅ User deleted successfully')
    } catch (error: any) {
      console.error('❌ Delete user failed:', error)
      throw new Error('حدث خطأ أثناء حذف المستخدم')
    }
  }

  // ==================== ORDER MANAGEMENT ====================

  /**
   * Get orders with user and product details using efficient JOIN view
   */
  static async getOrdersWithDetails(
    status?: 'pending' | 'processing' | 'completed' | 'cancelled' | 'all',
    page: number = 1,
    limit: number = 5
  ) {
    try {
      const from = (page - 1) * limit
      const to = from + limit - 1

      // Query orders directly
      let query = this.supabase
        .from('orders')
        .select(`
          *
        `)

      if (status && status !== 'all') {
        query = query.eq('status', status)
      }

      const { data, error } = await query
        .order('created_at', { ascending: false })
        .range(from, to)

      if (error) {
        throw new Error('Failed to fetch orders')
        throw new Error('Failed to fetch orders')
      }

      // Transform the data to match expected format and fetch additional details
      const transformedData = await Promise.all(data.map(async (order: any) => {
        // Fetch user details
        let userName = 'Unknown User'
        let userEmail = '<EMAIL>'
        try {
          const { data: userProfile } = await this.supabase
            .from('user_profiles')
            .select('name, email')
            .eq('id', order.user_id)
            .single()
          
          if (userProfile) {
            userName = userProfile.name || 'Unknown User'
            userEmail = userProfile.email || '<EMAIL>'
          }
        } catch (error) {
          // Silently handle error - use default values
        }
        
        // Fetch product details
        let productName = order.product_name || 'Unknown Product'
        let productCategory = 'Unknown'
        let productSubcategory = 'Unknown'
        try {
          const { data: product } = await this.supabase
            .from('products')
            .select('name, category, subcategory')
            .eq('id', order.product_id)
            .single()
          
          if (product) {
            productName = product.name || productName
            productCategory = product.category || 'Unknown'
            productSubcategory = product.subcategory || 'Unknown'
          }
        } catch (error) {
          // Silently handle error - use default values
        }
        
        return {
          id: order.id,
          user_id: order.user_id,
          product_id: order.product_id,
          game_id: order.game_id,
          amount: order.amount,
          price: order.price,
          status: order.status,
          notes: order.notes,
          admin_notes: order.admin_notes,
          created_at: order.created_at,
          updated_at: order.updated_at,
          completed_at: order.completed_at,
          user_name: userName,
          user_email: userEmail,
          product_name: productName,
          product_category: productCategory,
          product_subcategory: productSubcategory
        }
      }))

      console.log(`✅ SupabaseAdminService: Fetched ${transformedData.length} orders with details`)
      return transformedData
    } catch (error: any) {
      console.error('❌ Get orders with details failed:', error)
      throw new Error('حدث خطأ أثناء جلب الطلبات')
    }
  }

  /**
   * ✅ PERFORMANCE: Get paginated orders with server-side filtering and comprehensive search
   */
  static async getOrdersWithDetailsPaginated(
    status?: 'pending' | 'processing' | 'completed' | 'cancelled' | 'all',
    page: number = 1,
    pageSize: number = 20,
    search: string = ''
  ) {
    try {
      const from = (page - 1) * pageSize
      const to = from + pageSize - 1

      // ✅ FIXED: Start with simple query first, then enhance with joins
      let query = this.supabase
        .from('orders')
        .select('*', { count: 'exact' })
        .order('created_at', { ascending: false })
        .range(from, to)

      // Apply status filter
      if (status && status !== 'all') {
        query = query.eq('status', status)
      }

      // ✅ ENHANCED SEARCH: Handle exact ID matches and partial searches
      if (search && search.trim() !== '') {
        const searchTerm = search.trim()
        
        // Check if it looks like a UUID (for exact order ID matches)
        const isUUID = searchTerm.length >= 8 && searchTerm.includes('-')
        
        if (isUUID) {
          // For UUID-like searches, try exact match first, then other fields
          query = query.or(`
            id.eq.${searchTerm},
            game_id.ilike.%${searchTerm}%,
            amount.ilike.%${searchTerm}%,
            notes.ilike.%${searchTerm}%
          `.replace(/\s+/g, ''))
        } else {
          // For non-UUID searches, search in text fields only
          query = query.or(`
            game_id.ilike.%${searchTerm}%,
            amount.ilike.%${searchTerm}%,
            notes.ilike.%${searchTerm}%
          `.replace(/\s+/g, ''))
        }
      }

      const { data, error, count } = await query

      if (error) {
        return {
          orders: [],
          totalCount: 0,
          totalPages: 0
        }
      }

      // ✅ BACK TO WORKING APPROACH: Transform data with separate queries for user and product details
      const transformedData = await Promise.all(data.map(async (order: any) => {
        // Fetch user details
        let userName = 'Unknown User'
        let userEmail = '<EMAIL>'
        try {
          const { data: userProfile } = await this.supabase
            .from('user_profiles')
            .select('name, email')
            .eq('id', order.user_id)
            .single()
          
          if (userProfile) {
            userName = userProfile.name || 'Unknown User'
            userEmail = userProfile.email || '<EMAIL>'
          }
        } catch (error) {
          // Silently handle error - use default values
        }
        
        // Fetch product details
        let productName = order.product_name || 'Unknown Product'
        let productCategory = 'Unknown'
        let productSubcategory = 'Unknown'
        try {
          const { data: product } = await this.supabase
            .from('products')
            .select('name, category, subcategory')
            .eq('id', order.product_id)
            .single()
          
          if (product) {
            productName = product.name || productName
            productCategory = product.category || 'Unknown'
            productSubcategory = product.subcategory || 'Unknown'
          }
        } catch (error) {
          // Silently handle error - use default values
        }
        
        return {
          id: order.id,
          user_id: order.user_id,
          product_id: order.product_id,
          game_id: order.game_id,
          amount: order.amount,
          price: order.price,
          status: order.status,
          notes: order.notes,
          admin_notes: order.admin_notes,
          created_at: order.created_at,
          updated_at: order.updated_at,
          completed_at: order.completed_at,
          user_name: userName,
          user_email: userEmail,
          product_name: productName,
          product_category: productCategory,
          product_subcategory: productSubcategory
        }
      }))

      const totalCount = count || 0
      const totalPages = Math.ceil(totalCount / pageSize)

      return {
        orders: transformedData,
        totalCount,
        totalPages
      }
    } catch (error: any) {
      return {
        orders: [],
        totalCount: 0,
        totalPages: 0
      }
    }
  }

  /**
   * Update order status with transaction handling
   */
  static async updateOrderStatus(
    orderId: string,
    status: 'pending' | 'processing' | 'completed' | 'cancelled',
    adminId?: string,
    adminNotes?: string
  ): Promise<void> {
    try {
      console.log(`🔄 SupabaseAdminService: Starting order ${orderId} status update to ${status}`)

      // Get order details first
      const { data: orderData, error: orderError } = await this.supabase
        .from('orders')
        .select('*')
        .eq('id', orderId)
        .single()

      if (orderError) {
        console.error('❌ Failed to get order details:', orderError)
        throw new Error('Failed to get order details')
      }

      console.log(`📋 Current order status: ${orderData.status} -> ${status}`)

      const updateData: any = {
        status,
        updated_at: new Date().toISOString()
      }

      if (adminNotes) {
        updateData.admin_notes = adminNotes
      }

      if (status === 'completed') {
        updateData.completed_at = new Date().toISOString()
      }

      console.log(`🔧 Updating order with data:`, updateData)

      // Update order status with explicit return to verify update
      const { data: updatedData, error } = await this.supabase
        .from('orders')
        .update(updateData)
        .eq('id', orderId)
        .select()
        .single()

      if (error) {
        console.error('❌ Update order status error:', error)
        throw new Error('Failed to update order status')
      }

      console.log(`✅ Order updated successfully:`, updatedData)

      // Verify the update by fetching the order again
      const { data: verificationData, error: verificationError } = await this.supabase
        .from('orders')
        .select('status, updated_at')
        .eq('id', orderId)
        .single()

      if (verificationError) {
        console.error('❌ Failed to verify order update:', verificationError)
      } else {
        console.log(`🔍 Verification: Order ${orderId} status is now ${verificationData.status} (updated at ${verificationData.updated_at})`)
      }

      // Handle transaction creation/updates based on status change
      const { TransactionService } = await import('./transactions')

      if (status === 'cancelled' && orderData.status !== 'cancelled') {
        // Create refund transaction when order is cancelled
        await TransactionService.createOrderRefundTransaction(
          orderData.user_id,
          orderId,
          orderData.price,
          orderData.product_name || 'منتج',
          adminId,
          adminNotes || 'إلغاء الطلب'
        )
        console.log('✅ Refund transaction created for cancelled order')
      }
      
      // Note: Purchase transactions are already created when orders are placed
      // We don't create duplicate transactions when status changes to completed

      console.log('✅ Order status updated successfully with transaction handling')
    } catch (error: any) {
      console.error('❌ Update order status failed:', error)
      throw new Error('حدث خطأ أثناء تحديث حالة الطلب')
    }
  }

  /**
   * Create new order (admin bypass RLS)
   */
  static async createOrder(orderData: {
    userId: string
    productId: string
    gameId: string
    amount: string
    price: number
    notes?: string
  }): Promise<Order> {
    try {
      console.log('📝 Admin: Creating new order:', orderData)

      const { data, error } = await this.supabase
        .from('orders')
        .insert({
          user_id: orderData.userId,
          product_id: orderData.productId,
          game_id: orderData.gameId,
          amount: orderData.amount,
          price: orderData.price,
          status: 'pending',
          notes: orderData.notes || null
        })
        .select()
        .single()

      if (error) {
        console.error('❌ Admin create order error:', error)
        throw new Error('Failed to create order')
      }

      console.log('✅ Admin: Order created successfully')
      return this.mapSupabaseOrderToAppOrder(data)
    } catch (error: any) {
      console.error('❌ Admin create order failed:', error)
      throw new Error('حدث خطأ أثناء إنشاء الطلب')
    }
  }

  /**
   * Delete order
   */
  static async deleteOrder(orderId: string): Promise<void> {
    try {
      console.log(`🗑️ Deleting order ${orderId}`)

      const { error } = await this.supabase
        .from('orders')
        .delete()
        .eq('id', orderId)

      if (error) {
        console.error('❌ Delete order error:', error)
        throw new Error('Failed to delete order')
      }

      console.log('✅ Order deleted successfully')
    } catch (error: any) {
      console.error('❌ Delete order failed:', error)
      throw new Error('حدث خطأ أثناء حذف الطلب')
    }
  }

  // ==================== PRODUCT MANAGEMENT ====================

  /**
   * Get product by ID
   */
  static async getProductById(productId: string): Promise<Product | null> {
    try {
      console.log(`🛍️ Fetching product by ID: ${productId}`)

      const { data, error } = await this.supabase
        .from('products')
        .select('*')
        .eq('id', productId)
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          return null // Product not found
        }
        console.error('❌ Get product by ID error:', error)
        throw new Error('Failed to fetch product')
      }

      console.log(`✅ Product ${productId} fetched successfully`)
      return this.mapSupabaseProductToAppProduct(data)
    } catch (error: any) {
      console.error('❌ Get product by ID failed:', error)
      return null
    }
  }

  /**
   * Get all products
   */
  static async getAllProducts(): Promise<Product[]> {
    try {
      console.log('🛍️ Fetching all products')

      const { data, error } = await this.supabase
        .from('products')
        .select('*')
        .order('sort_order', { ascending: true })

      if (error) {
        console.error('❌ Get products error:', error)
        console.warn('⚠️ Returning empty products array due to error')
        return []
      }

      console.log(`✅ Fetched ${data.length} products`)
      return data.map(this.mapSupabaseProductToAppProduct)
    } catch (error: any) {
      console.error('❌ Get all products failed:', error)
      console.warn('⚠️ Returning empty products array due to error')
      return []
    }
  }

  /**
   * Create new product
   */
  static async createProduct(productData: any): Promise<Product> {
    try {
      console.log('➕ Creating new product:', productData.name)

      const { data, error } = await this.supabase
        .from('products')
        .insert({
          name: productData.name,
          price: productData.price,
          distributor_price: productData.distributorPrice || productData.price, // Default to regular price if not provided
          original_price: productData.originalPrice,
          discount_percentage: productData.discountPercentage,
          is_active: productData.isActive ?? true,
          is_popular: productData.isPopular ?? false,
          has_special_offer: productData.specialOffer ?? false,
          image_url: productData.imageUrl,
          sort_order: productData.sortOrder ?? 0,
          offer_start_date: productData.offerStartDate,
          offer_end_date: productData.offerEndDate
        })
        .select()
        .single()

      if (error) {
        console.error('❌ Create product error:', error)
        throw new Error('Failed to create product')
      }

      console.log('✅ Product created successfully')
      return this.mapSupabaseProductToAppProduct(data)
    } catch (error: any) {
      console.error('❌ Create product failed:', error)
      throw new Error('حدث خطأ أثناء إنشاء المنتج')
    }
  }

  /**
   * Update existing product
   */
  static async updateProduct(productId: string, updates: any): Promise<Product> {
    try {
      console.log(`🔄 Updating product ${productId}`)
      console.log(`📋 Update payload received:`, updates)

      // First, check if the product exists
      const { data: existingProduct, error: fetchError } = await this.supabase
        .from('products')
        .select('*')
        .eq('id', productId)
        .single()

      if (fetchError) {
        console.error('❌ Error fetching product before update:', fetchError)
        throw new Error(`Failed to fetch product: ${fetchError.message}`)
      }

      if (!existingProduct) {
        console.error('❌ Product not found:', productId)
        throw new Error('Product not found')
      }

      console.log(`📋 Current product details:`, {
        id: existingProduct.id,
        name: existingProduct.name,
        price: existingProduct.price,
        distributor_price: existingProduct.distributor_price
      })

      const updateData: any = {}
      if (updates.name) updateData.name = updates.name
      if (updates.price) updateData.price = updates.price
      if (updates.distributorPrice !== undefined) {
        console.log(`💰 Setting distributor price: ${updates.distributorPrice}`)
        updateData.distributor_price = updates.distributorPrice
      }
      if (updates.originalPrice) updateData.original_price = updates.originalPrice
      if (updates.discountPercentage) updateData.discount_percentage = updates.discountPercentage
      if (updates.isActive !== undefined) updateData.is_active = updates.isActive
      if (updates.isPopular !== undefined) updateData.is_popular = updates.isPopular
      if (updates.hasOffer !== undefined) updateData.has_special_offer = updates.hasOffer
      if (updates.specialOffer !== undefined) updateData.has_special_offer = updates.specialOffer
      if (updates.imageUrl !== undefined) updateData.image_url = updates.imageUrl
      if (updates.sortOrder !== undefined) updateData.sort_order = updates.sortOrder
      if (updates.offerStartDate !== undefined) updateData.offer_start_date = updates.offerStartDate
      if (updates.offerEndDate !== undefined) updateData.offer_end_date = updates.offerEndDate

      console.log(`📋 Final update data:`, updateData)

      const { data, error } = await this.supabase
        .from('products')
        .update(updateData)
        .eq('id', productId)
        .select()
        .single()

      if (error) {
        console.error('❌ Update product error:', error)
        console.error('❌ Error details:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        })
        throw new Error(`Failed to update product: ${error.message}`)
      }

      // Verify the update worked
      console.log(`📋 Updated product data:`, {
        id: data.id,
        name: data.name,
        price: data.price,
        distributor_price: data.distributor_price
      })

      console.log('✅ Product updated successfully')
      return this.mapSupabaseProductToAppProduct(data)
    } catch (error: any) {
      console.error('❌ Update product failed:', error)
      throw new Error('حدث خطأ أثناء تحديث المنتج')
    }
  }

  /**
   * Delete product
   */
  static async deleteProduct(productId: string): Promise<void> {
    try {
      console.log(`🗑️ Deleting product ${productId}`)

      const { error } = await this.supabase
        .from('products')
        .delete()
        .eq('id', productId)

      if (error) {
        console.error('❌ Delete product error:', error)
        throw new Error('Failed to delete product')
      }

      console.log('✅ Product deleted successfully')
    } catch (error: any) {
      console.error('❌ Delete product failed:', error)
      throw new Error('حدث خطأ أثناء حذف المنتج')
    }
  }

  // ==================== SYSTEM SETTINGS ====================

  /**
   * Get system settings
   */
  static async getSystemSettings() {
    try {
      console.log('⚙️ Fetching system settings from Supabase')

      const { data, error } = await this.supabase
        .from('system_settings')
        .select('*')

      if (error) {
        console.error('❌ Get system settings error:', error)
        throw new Error('Failed to fetch system settings')
      }

      // Convert array of key-value pairs to object
      const settings: any = {}
      data.forEach((setting: any) => {
        try {
          // Try to parse JSON values
          settings[setting.key] = JSON.parse(setting.value)
        } catch {
          // If not JSON, use as string
          settings[setting.key] = setting.value
        }
      })

      console.log('✅ System settings fetched successfully')
      return {
        siteName: settings.siteName || 'نيران كارد',
        siteDescription: settings.siteDescription || 'منصة شراء العملات الرقمية للألعاب',
        maintenanceMode: settings.maintenanceMode || false,
        allowRegistrations: settings.allowRegistrations !== undefined ? settings.allowRegistrations : true,
        supportWhatsApp: settings.supportWhatsApp || '+966501234567',
        supportEmail: settings.supportEmail || '<EMAIL>',
        language: settings.language || 'ar',
        autoApproveUsers: settings.autoApproveUsers !== undefined ? settings.autoApproveUsers : true,
        updatedAt: new Date().toISOString()
      }
    } catch (error: any) {
      console.error('❌ Get system settings failed:', error)
      throw new Error('حدث خطأ أثناء جلب إعدادات النظام')
    }
  }

  /**
   * Update system settings
   */
  static async updateSystemSettings(settings: any): Promise<void> {
    try {
      console.log('🔄 Updating system settings')

      // Convert settings object to array of key-value pairs
      const settingsArray = Object.entries(settings).map(([key, value]) => ({
        key,
        value: typeof value === 'object' ? JSON.stringify(value) : String(value),
        type: typeof value,
        is_public: true
      }))

      // Use upsert to insert or update settings
      for (const setting of settingsArray) {
        const { error } = await this.supabase
          .from('system_settings')
          .upsert(setting, { onConflict: 'key' })

        if (error) {
          console.error('❌ Update setting error:', error)
          throw new Error(`Failed to update setting: ${setting.key}`)
        }
      }

      console.log('✅ System settings updated successfully')
    } catch (error: any) {
      console.error('❌ Update system settings failed:', error)
      throw new Error('حدث خطأ أثناء تحديث إعدادات النظام')
    }
  }

  // ==================== HELPER METHODS ====================

  /**
   * Map Supabase order to app order format
   */
  private static mapSupabaseOrderToAppOrder(order: any): Order {
    return {
      id: order.id,
      userId: order.user_id,
      gameId: order.game_id,
      product: order.product_name || 'Unknown Product',
      productId: order.product_id,
      amount: order.amount,
      price: order.price,
      status: order.status,
      createdAt: order.created_at,
      updatedAt: order.updated_at,
      completedAt: order.completed_at,
      notes: order.notes,
      adminNotes: order.admin_notes
    }
  }

  /**
   * Map Supabase user data to app User type
   */
  private static mapSupabaseUserToAppUser(supabaseUser: any): User {
    return {
      id: supabaseUser.id,
      email: supabaseUser.email,
      name: supabaseUser.name,
      role: supabaseUser.role,
      status: supabaseUser.status,
      balance: supabaseUser.balance,
      createdAt: supabaseUser.created_at,
      updatedAt: supabaseUser.updated_at,
    }
  }

  /**
   * Map Supabase product data to app Product type
   */
  private static mapSupabaseProductToAppProduct(supabaseProduct: any): Product {
    return {
      id: supabaseProduct.id,
      name: supabaseProduct.name,
      category: 'gems', // Default category since we removed this field
      price: supabaseProduct.price,
      distributorPrice: supabaseProduct.distributor_price || supabaseProduct.price, // Fallback to regular price if not set
      description: '', // Default empty description since we removed this field
      isActive: supabaseProduct.is_active,
      imageUrl: supabaseProduct.image_url,
      gameType: 'gems', // Default game type
      sortOrder: supabaseProduct.sort_order,
      hasOffer: supabaseProduct.has_special_offer, // Map 'has_special_offer' to 'hasOffer'
      originalPrice: supabaseProduct.original_price,
      discountPercentage: supabaseProduct.discount_percentage,
      offerStartDate: supabaseProduct.offer_start_date,
      offerEndDate: supabaseProduct.offer_end_date,
      // Enhanced UI fields
      popular: Boolean(supabaseProduct.is_popular),
      specialOffer: Boolean(supabaseProduct.has_special_offer),
      createdAt: supabaseProduct.created_at,
      updatedAt: supabaseProduct.updated_at,
    }
  }
}
